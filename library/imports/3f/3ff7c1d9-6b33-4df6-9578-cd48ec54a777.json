{"__type__": "cc.TextAsset", "_name": "README_SinglePlayer", "_objFlags": 0, "_native": "", "text": "# 单机模式控制器使用说明\n\n## 概述\n\n`SinglePlayerController` 是一个专门为扫雷游戏单机模式设计的控制器，支持多种地图大小配置，并通过 `LevelClickBlock` 消息与后端通信。\n\n## 主要特性\n\n1. **多地图支持**: 支持 8x8、9x9、9x10、10x10 四种地图大小\n2. **独立消息系统**: 使用 `LevelClickBlock` 消息，与联机模式完全分离\n3. **即时响应**: 操作立即生效，无延迟展示\n4. **自由标记**: 支持标记状态的自由切换\n5. **无时间限制**: 没有回合制和倒计时限制\n\n## 地图配置\n\n### 支持的地图类型\n\n| 地图类型 | 棋盘大小 | 格子大小 | 行列数 |\n|---------|---------|---------|-------|\n| 8x8     | 752x845 | 88x88   | 8x8   |\n| 9x9     | 752x747 | 76x76   | 9x9   |\n| 9x10    | 752x830 | 78x78   | 9x10  |\n| 10x10   | 752x745 | 69x69   | 10x10 |\n\n## 使用方法\n\n### 1. 在编辑器中配置\n\n在 `LevelPageController` 中添加 `SinglePlayerController` 组件的引用：\n\n```typescript\n@property(SinglePlayerController)\nsinglePlayerController: SinglePlayerController = null;\n```\n\n### 2. 设置预制体\n\n在 `SinglePlayerController` 中配置以下预制体：\n\n- `boomPrefab`: 炸弹预制体\n- `biaojiPrefab`: 标记预制体\n- `boom1Prefab` ~ `boom8Prefab`: 数字1-8预制体\n\n### 3. 初始化控制器\n\n```typescript\n// 设置棋盘节点\nsinglePlayerController.setBoardNode(boardNode);\n\n// 设置地图类型\nsinglePlayerController.setMapType(\"8x8\");\n\n// 清空之前的预制体\nsinglePlayerController.clearAllPrefabs();\n```\n\n## 操作说明\n\n### 点击操作\n- **短按**: 挖掘方块 (action = 1)\n- **长按**: 标记/取消标记 (action = 2)\n\n### 消息流程\n\n1. **发送消息**: \n   - 消息类型: `LevelClickBlock`\n   - 参数: `{x, y, action}`\n\n2. **接收响应**:\n   - 消息类型: `LevelClickBlock`\n   - 响应: `{x, y, action, result}`\n\n### 结果处理\n\n- `result = \"mine\"`: 显示炸弹预制体\n- `result = \"marked\"`: 显示标记预制体\n- `result = \"unmarked\"`: 移除标记预制体\n- `result = 数字`: 显示对应数字预制体\n\n## 与联机模式的区别\n\n| 特性 | 联机模式 | 单机模式 |\n|-----|---------|---------|\n| 消息类型 | `ClickBlock` | `LevelClickBlock` |\n| 头像生成 | 需要 | 不需要 |\n| 时间限制 | 有回合制 | 无限制 |\n| 标记切换 | 受限 | 自由切换 |\n| 响应速度 | 有延迟 | 即时响应 |\n\n## API 参考\n\n### 公共方法\n\n- `setMapType(mapType: string)`: 设置地图类型\n- `setBoardNode(boardNode: Node)`: 设置棋盘节点\n- `clearAllPrefabs()`: 清空所有预制体\n\n### 私有方法\n\n- `initBoard()`: 初始化棋盘\n- `onGridClick(x, y)`: 处理格子点击\n- `onGridLongPress(x, y)`: 处理格子长按\n- `createBoomPrefab(x, y)`: 创建炸弹预制体\n- `createBiaojiPrefab(x, y)`: 创建标记预制体\n- `createNumberPrefab(x, y, number)`: 创建数字预制体\n\n## 注意事项\n\n1. 确保在编辑器中正确配置所有预制体引用\n2. 棋盘节点必须包含足够的子节点作为格子\n3. 后端需要实现 `LevelClickBlock` 消息处理\n4. 特殊关卡（S001-S006）暂时不使用单机控制器\n"}