{"version": 3, "sources": ["assets/resources/i18n/zh_CN.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACxB,SAAS;IACL,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE,OAAO;IAClB,mBAAmB,EAAE,UAAU;IAC/B,iBAAiB,EAAE,QAAQ;IAC3B,YAAY,EAAE,MAAM;IACpB,UAAU,EAAE,MAAM;IAClB,eAAe,EAAE,OAAO;IACxB,iBAAiB,EAAE,UAAU;IAC7B,gBAAgB,EAAE,OAAO;IACzB,uBAAuB,EAAE,QAAQ;IACjC,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,aAAa;IAE1B,gBAAgB,EAAE,QAAQ;IAC1B,sBAAsB,EAAE,QAAQ;IAChC,QAAQ,EAAE,eAAe;IAEzB,MAAM,EAAC,MAAM;IACb,QAAQ,EAAC,MAAM;IACf,SAAS,EAAC,IAAI;IACd,SAAS,EAAC,IAAI;IACd,UAAU,EAAC,MAAM;IACjB,MAAM,EAAC,IAAI;IACX,OAAO,EAAC,IAAI;IACZ,QAAQ,EAAC,IAAI;IACb,IAAI,EAAC,IAAI;IACT,KAAK,EAAC,IAAI;IACV,KAAK,EAAC,IAAI;IACV,KAAK,EAAC,IAAI;IACV,IAAI,EAAC,IAAI;IACT,MAAM,EAAC,IAAI;IACX,IAAI,EAAC,IAAI;IACT,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,KAAK;IACjB,IAAI,EAAC,IAAI;IACT,WAAW,EAAC,MAAM;IAClB,cAAc,EAAC,OAAO;IACtB,UAAU,EAAC,OAAO;IAClB,iBAAiB,EAAC,OAAO;IACzB,IAAI,EAAC,IAAI;IACT,OAAO,EAAC,IAAI;IACZ,MAAM,EAAC,IAAI;IACX,OAAO,EAAC,IAAI;IACZ,KAAK,EAAC,IAAI;IAEV,SAAS,EAAC,KAAK;IAEf,OAAO,EAAC,MAAM;IAEd,UAAU,EAAC,MAAM;IACjB,WAAW,EAAC,MAAM;IAElB,MAAM,EAAC,OAAO;IACd,MAAM,EAAC,MAAM;IACb,MAAM,EAAC,KAAK;IACZ,MAAM,EAAC,OAAO;IACd,MAAM,EAAC,KAAK;IACZ,MAAM,EAAC,KAAK;IAEZ,MAAM,EAAC,gHAAgH;IACvH,MAAM,EAAC,iBAAiB;IACxB,MAAM,EAAC,eAAe;IACtB,MAAM,EAAC,8BAA8B;IACrC,MAAM,EAAC,iCAAiC;IACxC,MAAM,EAAC,oDAAoD;IAG3D,MAAM,EAAC,OAAO;IACd,MAAM,EAAC,OAAO;IACd,MAAM,EAAC,OAAO;IACd,MAAM,EAAC,OAAO;IACd,MAAM,EAAC,KAAK;IACZ,MAAM,EAAC,OAAO;IAEd,MAAM,EAAC,wGAAwG;IAC/G,MAAM,EAAC,UAAU;IACjB,MAAM,EAAC,KAAK;IACZ,MAAM,EAAC,wBAAwB;IAC/B,MAAM,EAAC,gCAAgC;IACvC,MAAM,EAAC,uJAAuJ;CAKjK,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n//这部分是通用的\n    kickout1: '您被请出房间',\n    LeaveRoom: '房间已解散',\n    InsufficientBalance: '余额不足，去充值',\n    GameRouteNotFound: '游戏线路异常',\n    NetworkError: '网络异常',\n    RoomIsFull: '房间已满',\n    EnterRoomNumber: '输入房间号',\n    GetUserInfoFailed: '获取用户信息失败',\n    RoomDoesNotExist: '房间不存在',\n    FailedToDeductGoldCoins: '扣除金币失败',\n    ExitApplication: '确定退出游戏？',\n    QuitTheGame: '退出后将无法返回游戏。',\n\n    NotEnoughPlayers: '玩家数量不足',\n    TheGameIsFullOfPlayers: '玩家数量已满',\n    kickout2: '是否将 {0} 请出房间?',//踢出玩家文案\n\n    upSeat:'加入游戏',\n    downSeat:'退出游戏', \n    startGame:'开始', \n    readyGame:'准备', \n    cancelGame:'取消准备', \n    cancel:'取消', \n    confirm:'确定', \n    kickout3:'踢出', \n    back:'返回',//返回\n    leave:'退出',\n    music:'音乐',\n    sound:'音效',\n    join:'加入', //加入\n    create:'创建', //创建\n    auto:'匹配',\n    Room:'房间',\n    room_number:'房间号', //房间号\n    copy:'复制', //复制\n    game_amount:'游戏费用', //游戏费用\n    player_numbers:'玩家数量:', //玩家数量\n    room_exist:'房间不存在',//房间不存在\n    enter_room_number:'输入房间号',//输入房间号\n    free:'免费',\n    players:'玩家', //玩家\n    Player:'玩家',\n    Tickets:'门票',\n    Empty:'空位',\n\n    nextlevel:'下一关',//下一关\n\n    relevel:'再玩一次', //再来一局\n\n    danjiguize:\"单机规则\",\n    lianjuguize:\"联机规则\",\n\n    dtips1:\"游戏简介：\",\n    dtips2:\"安全区：\",\n    dtips3:\"雷区：\",\n    dtips4:\"游戏目标：\",\n    dtips5:\"标记：\",\n    dtips6:\"提示：\",\n\n    dinfo1:\"游戏中存在一些格子，翻开后分为数字格子，空白格子和地雷，玩家可以通过点击翻开新格子，数字格子可以同帮助玩家获得周围相邻格子中存在的地雷数量，若翻到空白格子会继续扩散翻牌，直到翻到数字格子才停止，利用游戏规则，顺利通关吧。\",\n    dinfo2:\"数字格子与空白格子统称为安全区\",\n    dinfo3:\"翻到雷区，会导致游戏失败。\",\n    dinfo4:\"在不触发任何雷区的情况下，揭示游戏区域中的所有安全格子。\",\n    dinfo5:\"玩家可以通过对格子长按，插旗子来标记雷区，标记不会翻开该格子。\",\n    dinfo6:\"玩家每局可获得一次提示，仅本局生效，点击提示可以帮助玩家显示出一个安全的格子。一局最多使用4次提示。\",\n\n\n    ltips1:\"游戏简介：\",\n    ltips2:\"游戏人数：\",\n    ltips3:\"回合时间：\",\n    ltips4:\"游戏目标：\",\n    ltips5:\"标记：\",\n    ltips6:\"得分规则：\",\n\n    linfo1:\"与单机模式相同，格子的样式一致。每个回合所有玩家同时选择任意一个格子，时间结束后或所有人全部选择完毕后，展示所有人的选择情况并根据格子的内容进行加减分，待所有格子都被选完，游戏结束。争取得到更多的分数吧！\",\n    linfo2:\"2人/3人/4人\",\n    linfo3:\"20秒\",\n    linfo4:\"利用游戏规则，进行得分，分数最高者获得胜利。\",\n    linfo5:\"长按可对格子进行雷区标记，若该格子为雷区，则可额外获得加分。\",\n    linfo6:\"1. 每回合最先选择格子的玩家获得1分。/n2. 单击翻开格子，若为安全格子则加6分，为雷区则扣12分。/n3. 长按进行标记翻开格子，若为雷区则加10分，为安全区则不加分。/n4. 多人同时选择一个格子，根据选择的对错结果，进行平均加分，例如两人单击选择同一个格子，该格子为安全区，则每人得3分。\",\n\n\n\n\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.zh_CN = language;"]}