"use strict";
cc._RF.push(module, '0348ePkuwdBkLQDsmntKZQp', 'SinglePlayerTest');
// scripts/level/SinglePlayerTest.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SinglePlayerTest = void 0;
var cc_1 = require("cc");
var SinglePlayerController_1 = require("./SinglePlayerController");
var ccclass = cc_1._decorator.ccclass, property = cc_1._decorator.property;
/**
 * 单机模式控制器测试脚本
 * 用于测试和演示SinglePlayerController的功能
 */
var SinglePlayerTest = /** @class */ (function (_super) {
    __extends(SinglePlayerTest, _super);
    function SinglePlayerTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.singlePlayerController = null;
        _this.testBoardNode = null;
        _this.test8x8Button = null;
        _this.test9x9Button = null;
        _this.test9x10Button = null;
        _this.test10x10Button = null;
        _this.clearButton = null;
        _this.statusLabel = null;
        return _this;
    }
    SinglePlayerTest.prototype.onLoad = function () {
        this.setupButtons();
        this.updateStatus("单机模式控制器测试就绪");
    };
    SinglePlayerTest.prototype.setupButtons = function () {
        var _this = this;
        if (this.test8x8Button) {
            this.test8x8Button.node.on('click', function () { return _this.testMapType("8x8"); }, this);
        }
        if (this.test9x9Button) {
            this.test9x9Button.node.on('click', function () { return _this.testMapType("9x9"); }, this);
        }
        if (this.test9x10Button) {
            this.test9x10Button.node.on('click', function () { return _this.testMapType("9x10"); }, this);
        }
        if (this.test10x10Button) {
            this.test10x10Button.node.on('click', function () { return _this.testMapType("10x10"); }, this);
        }
        if (this.clearButton) {
            this.clearButton.node.on('click', this.clearAllPrefabs, this);
        }
    };
    SinglePlayerTest.prototype.testMapType = function (mapType) {
        if (!this.singlePlayerController) {
            this.updateStatus("错误: SinglePlayerController 未配置");
            return;
        }
        if (!this.testBoardNode) {
            this.updateStatus("错误: 测试棋盘节点未配置");
            return;
        }
        try {
            // 设置棋盘节点
            this.singlePlayerController.setBoardNode(this.testBoardNode);
            // 设置地图类型
            this.singlePlayerController.setMapType(mapType);
            // 清空之前的预制体
            this.singlePlayerController.clearAllPrefabs();
            this.updateStatus("\u5DF2\u5207\u6362\u5230 " + mapType + " \u5730\u56FE\u6A21\u5F0F");
            console.log("\u6D4B\u8BD5: \u5207\u6362\u5230 " + mapType + " \u5730\u56FE\u6A21\u5F0F");
        }
        catch (error) {
            this.updateStatus("\u9519\u8BEF: " + error.message);
            console.error("测试失败:", error);
        }
    };
    SinglePlayerTest.prototype.clearAllPrefabs = function () {
        if (!this.singlePlayerController) {
            this.updateStatus("错误: SinglePlayerController 未配置");
            return;
        }
        try {
            this.singlePlayerController.clearAllPrefabs();
            this.updateStatus("已清空所有预制体");
            console.log("测试: 清空所有预制体");
        }
        catch (error) {
            this.updateStatus("\u9519\u8BEF: " + error.message);
            console.error("清空失败:", error);
        }
    };
    SinglePlayerTest.prototype.updateStatus = function (message) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("[SinglePlayerTest] " + message);
    };
    /**
     * 模拟后端响应测试
     */
    SinglePlayerTest.prototype.simulateBackendResponse = function (x, y, action, result) {
        if (!this.singlePlayerController) {
            console.error("SinglePlayerController 未配置");
            return;
        }
        // 模拟后端响应数据
        var mockResponse = {
            msgId: "LevelClickBlock",
            data: {
                x: x,
                y: y,
                action: action,
                result: result
            }
        };
        console.log("模拟后端响应:", mockResponse);
        // 直接调用消息处理方法（仅用于测试）
        // 注意：这里需要访问私有方法，实际使用中应该通过WebSocket接收消息
        // this.singlePlayerController['handleLevelClickBlockResponse'](mockResponse.data);
    };
    /**
     * 测试各种操作结果
     */
    SinglePlayerTest.prototype.runTestSuite = function () {
        var _this = this;
        console.log("开始运行测试套件...");
        // 测试挖掘操作 - 数字结果
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(0, 0, 1, 1);
            console.log("测试: 挖掘操作 - 显示数字1");
        }, 1);
        // 测试挖掘操作 - 炸弹结果
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(1, 0, 1, "mine");
            console.log("测试: 挖掘操作 - 显示炸弹");
        }, 2);
        // 测试标记操作
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(2, 0, 2, "marked");
            console.log("测试: 标记操作 - 显示标记");
        }, 3);
        // 测试取消标记操作
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(2, 0, 2, "unmarked");
            console.log("测试: 取消标记操作 - 移除标记");
        }, 4);
        this.updateStatus("测试套件运行中...");
    };
    __decorate([
        property(SinglePlayerController_1.SinglePlayerController)
    ], SinglePlayerTest.prototype, "singlePlayerController", void 0);
    __decorate([
        property(cc_1.Node)
    ], SinglePlayerTest.prototype, "testBoardNode", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test8x8Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test9x9Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test9x10Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test10x10Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "clearButton", void 0);
    __decorate([
        property(cc_1.Label)
    ], SinglePlayerTest.prototype, "statusLabel", void 0);
    SinglePlayerTest = __decorate([
        ccclass('SinglePlayerTest')
    ], SinglePlayerTest);
    return SinglePlayerTest;
}(cc_1.Component));
exports.SinglePlayerTest = SinglePlayerTest;

cc._RF.pop();