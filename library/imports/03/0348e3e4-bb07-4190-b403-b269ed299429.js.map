{"version": 3, "sources": ["assets/scripts/level/SinglePlayerTest.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yBAAgE;AAChE,mEAAkE;AAE1D,IAAA,OAAO,GAAe,eAAU,QAAzB,EAAE,QAAQ,GAAK,eAAU,SAAf,CAAgB;AAEzC;;;GAGG;AAEH;IAAsC,oCAAS;IAA/C;QAAA,qEAsKC;QAnKG,4BAAsB,GAA2B,IAAI,CAAC;QAGtD,mBAAa,GAAS,IAAI,CAAC;QAG3B,mBAAa,GAAW,IAAI,CAAC;QAG7B,mBAAa,GAAW,IAAI,CAAC;QAG7B,oBAAc,GAAW,IAAI,CAAC;QAG9B,qBAAe,GAAW,IAAI,CAAC;QAG/B,iBAAW,GAAW,IAAI,CAAC;QAG3B,iBAAW,GAAU,IAAI,CAAC;;IA8I9B,CAAC;IA5IG,iCAAM,GAAN;QACI,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAEO,uCAAY,GAApB;QAAA,iBAoBC;QAnBG,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAvB,CAAuB,EAAE,IAAI,CAAC,CAAC;SAC5E;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAvB,CAAuB,EAAE,IAAI,CAAC,CAAC;SAC5E;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAxB,CAAwB,EAAE,IAAI,CAAC,CAAC;SAC9E;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAzB,CAAyB,EAAE,IAAI,CAAC,CAAC;SAChF;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SACjE;IACL,CAAC;IAEO,sCAAW,GAAnB,UAAoB,OAAe;QAC/B,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAC;YACpD,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;YACnC,OAAO;SACV;QAED,IAAI;YACA,SAAS;YACT,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE7D,SAAS;YACT,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEhD,WAAW;YACX,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,CAAC;YAE9C,IAAI,CAAC,YAAY,CAAC,8BAAQ,OAAO,8BAAO,CAAC,CAAC;YAE1C,OAAO,CAAC,GAAG,CAAC,sCAAW,OAAO,8BAAO,CAAC,CAAC;SAE1C;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,mBAAO,KAAK,CAAC,OAAS,CAAC,CAAC;YAC1C,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACjC;IACL,CAAC;IAEO,0CAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAC;YACpD,OAAO;SACV;QAED,IAAI;YACA,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;SAC9B;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,mBAAO,KAAK,CAAC,OAAS,CAAC,CAAC;YAC1C,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACjC;IACL,CAAC;IAEO,uCAAY,GAApB,UAAqB,OAAe;QAChC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;SACrC;QACD,OAAO,CAAC,GAAG,CAAC,wBAAsB,OAAS,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,kDAAuB,GAA9B,UAA+B,CAAS,EAAE,CAAS,EAAE,MAAc,EAAE,MAAuB;QACxF,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC5C,OAAO;SACV;QAED,WAAW;QACX,IAAM,YAAY,GAAG;YACjB,KAAK,EAAE,iBAAiB;YACxB,IAAI,EAAE;gBACF,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;gBACJ,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;aACjB;SACJ,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAErC,oBAAoB;QACpB,uCAAuC;QACvC,mFAAmF;IACvF,CAAC;IAED;;OAEG;IACI,uCAAY,GAAnB;QAAA,iBA4BC;QA3BG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE3B,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,SAAS;QACT,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,WAAW;QACX,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAlKD;QADC,QAAQ,CAAC,+CAAsB,CAAC;oEACqB;IAGtD;QADC,QAAQ,CAAC,SAAI,CAAC;2DACY;IAG3B;QADC,QAAQ,CAAC,WAAM,CAAC;2DACY;IAG7B;QADC,QAAQ,CAAC,WAAM,CAAC;2DACY;IAG7B;QADC,QAAQ,CAAC,WAAM,CAAC;4DACa;IAG9B;QADC,QAAQ,CAAC,WAAM,CAAC;6DACc;IAG/B;QADC,QAAQ,CAAC,WAAM,CAAC;yDACU;IAG3B;QADC,QAAQ,CAAC,UAAK,CAAC;yDACU;IAxBjB,gBAAgB;QAD5B,OAAO,CAAC,kBAAkB,CAAC;OACf,gBAAgB,CAsK5B;IAAD,uBAAC;CAtKD,AAsKC,CAtKqC,cAAS,GAsK9C;AAtKY,4CAAgB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { _decorator, Component, Node, Button, Label } from 'cc';\nimport { SinglePlayerController } from './SinglePlayerController';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 单机模式控制器测试脚本\n * 用于测试和演示SinglePlayerController的功能\n */\n@ccclass('SinglePlayerTest')\nexport class SinglePlayerTest extends Component {\n\n    @property(SinglePlayerController)\n    singlePlayerController: SinglePlayerController = null;\n\n    @property(Node)\n    testBoardNode: Node = null;\n\n    @property(Button)\n    test8x8Button: Button = null;\n\n    @property(Button)\n    test9x9Button: Button = null;\n\n    @property(Button)\n    test9x10Button: Button = null;\n\n    @property(Button)\n    test10x10Button: Button = null;\n\n    @property(Button)\n    clearButton: Button = null;\n\n    @property(Label)\n    statusLabel: Label = null;\n\n    onLoad() {\n        this.setupButtons();\n        this.updateStatus(\"单机模式控制器测试就绪\");\n    }\n\n    private setupButtons() {\n        if (this.test8x8Button) {\n            this.test8x8Button.node.on('click', () => this.testMapType(\"8x8\"), this);\n        }\n\n        if (this.test9x9Button) {\n            this.test9x9Button.node.on('click', () => this.testMapType(\"9x9\"), this);\n        }\n\n        if (this.test9x10Button) {\n            this.test9x10Button.node.on('click', () => this.testMapType(\"9x10\"), this);\n        }\n\n        if (this.test10x10Button) {\n            this.test10x10Button.node.on('click', () => this.testMapType(\"10x10\"), this);\n        }\n\n        if (this.clearButton) {\n            this.clearButton.node.on('click', this.clearAllPrefabs, this);\n        }\n    }\n\n    private testMapType(mapType: string) {\n        if (!this.singlePlayerController) {\n            this.updateStatus(\"错误: SinglePlayerController 未配置\");\n            return;\n        }\n\n        if (!this.testBoardNode) {\n            this.updateStatus(\"错误: 测试棋盘节点未配置\");\n            return;\n        }\n\n        try {\n            // 设置棋盘节点\n            this.singlePlayerController.setBoardNode(this.testBoardNode);\n            \n            // 设置地图类型\n            this.singlePlayerController.setMapType(mapType);\n            \n            // 清空之前的预制体\n            this.singlePlayerController.clearAllPrefabs();\n            \n            this.updateStatus(`已切换到 ${mapType} 地图模式`);\n            \n            console.log(`测试: 切换到 ${mapType} 地图模式`);\n            \n        } catch (error) {\n            this.updateStatus(`错误: ${error.message}`);\n            console.error(\"测试失败:\", error);\n        }\n    }\n\n    private clearAllPrefabs() {\n        if (!this.singlePlayerController) {\n            this.updateStatus(\"错误: SinglePlayerController 未配置\");\n            return;\n        }\n\n        try {\n            this.singlePlayerController.clearAllPrefabs();\n            this.updateStatus(\"已清空所有预制体\");\n            console.log(\"测试: 清空所有预制体\");\n        } catch (error) {\n            this.updateStatus(`错误: ${error.message}`);\n            console.error(\"清空失败:\", error);\n        }\n    }\n\n    private updateStatus(message: string) {\n        if (this.statusLabel) {\n            this.statusLabel.string = message;\n        }\n        console.log(`[SinglePlayerTest] ${message}`);\n    }\n\n    /**\n     * 模拟后端响应测试\n     */\n    public simulateBackendResponse(x: number, y: number, action: number, result: string | number) {\n        if (!this.singlePlayerController) {\n            console.error(\"SinglePlayerController 未配置\");\n            return;\n        }\n\n        // 模拟后端响应数据\n        const mockResponse = {\n            msgId: \"LevelClickBlock\",\n            data: {\n                x: x,\n                y: y,\n                action: action,\n                result: result\n            }\n        };\n\n        console.log(\"模拟后端响应:\", mockResponse);\n        \n        // 直接调用消息处理方法（仅用于测试）\n        // 注意：这里需要访问私有方法，实际使用中应该通过WebSocket接收消息\n        // this.singlePlayerController['handleLevelClickBlockResponse'](mockResponse.data);\n    }\n\n    /**\n     * 测试各种操作结果\n     */\n    public runTestSuite() {\n        console.log(\"开始运行测试套件...\");\n        \n        // 测试挖掘操作 - 数字结果\n        this.scheduleOnce(() => {\n            this.simulateBackendResponse(0, 0, 1, 1);\n            console.log(\"测试: 挖掘操作 - 显示数字1\");\n        }, 1);\n\n        // 测试挖掘操作 - 炸弹结果\n        this.scheduleOnce(() => {\n            this.simulateBackendResponse(1, 0, 1, \"mine\");\n            console.log(\"测试: 挖掘操作 - 显示炸弹\");\n        }, 2);\n\n        // 测试标记操作\n        this.scheduleOnce(() => {\n            this.simulateBackendResponse(2, 0, 2, \"marked\");\n            console.log(\"测试: 标记操作 - 显示标记\");\n        }, 3);\n\n        // 测试取消标记操作\n        this.scheduleOnce(() => {\n            this.simulateBackendResponse(2, 0, 2, \"unmarked\");\n            console.log(\"测试: 取消标记操作 - 移除标记\");\n        }, 4);\n\n        this.updateStatus(\"测试套件运行中...\");\n    }\n}\n"]}