"use strict";
cc._RF.push(module, '882ddIZzIlMnadRsMiIVVUr', 'SinglePlayerController');
// scripts/level/SinglePlayerController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SinglePlayerController = /** @class */ (function (_super) {
    __extends(SinglePlayerController, _super);
    function SinglePlayerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardNode = null; // 棋盘节点
        _this.boomPrefab = null; // 炸弹预制体
        _this.biaojiPrefab = null; // 标记预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 地图配置
        _this.mapConfigs = {
            "8x8": {
                boardSize: { width: 752, height: 845 },
                gridSize: { width: 88, height: 88 },
                boardDimensions: { rows: 8, cols: 8 }
            },
            "9x9": {
                boardSize: { width: 752, height: 747 },
                gridSize: { width: 76, height: 76 },
                boardDimensions: { rows: 9, cols: 9 }
            },
            "9x10": {
                boardSize: { width: 752, height: 830 },
                gridSize: { width: 78, height: 78 },
                boardDimensions: { rows: 9, cols: 10 }
            },
            "10x10": {
                boardSize: { width: 752, height: 745 },
                gridSize: { width: 69, height: 69 },
                boardDimensions: { rows: 10, cols: 10 }
            }
        };
        // 当前地图配置
        _this.currentMapConfig = null;
        _this.currentMapType = "8x8"; // 默认8x8
        // 格子数据存储
        _this.gridData = [];
        _this.gridNodes = [];
        return _this;
    }
    SinglePlayerController.prototype.onLoad = function () {
        // 注册消息监听
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
        // 设置默认地图配置
        this.setMapType(this.currentMapType);
        this.initBoard();
    };
    SinglePlayerController.prototype.onDestroy = function () {
        // 取消消息监听
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 设置地图类型
     * @param mapType 地图类型 "8x8", "9x9", "9x10", "10x10"
     */
    SinglePlayerController.prototype.setMapType = function (mapType) {
        if (this.mapConfigs[mapType]) {
            this.currentMapType = mapType;
            this.currentMapConfig = this.mapConfigs[mapType];
            console.log("\u8BBE\u7F6E\u5730\u56FE\u7C7B\u578B\u4E3A: " + mapType, this.currentMapConfig);
            // 重新初始化棋盘
            if (this.boardNode) {
                this.initBoard();
            }
        }
        else {
            console.error("\u4E0D\u652F\u6301\u7684\u5730\u56FE\u7C7B\u578B: " + mapType);
        }
    };
    /**
     * 设置棋盘节点
     * @param boardNode 棋盘节点
     */
    SinglePlayerController.prototype.setBoardNode = function (boardNode) {
        this.boardNode = boardNode;
        if (this.currentMapConfig) {
            this.initBoard();
        }
    };
    /**
     * 初始化棋盘
     */
    SinglePlayerController.prototype.initBoard = function () {
        if (!this.currentMapConfig || !this.boardNode) {
            console.error("地图配置或棋盘节点未设置");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 初始化格子数据
        this.gridData = [];
        this.gridNodes = [];
        for (var x = 0; x < cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    hasPlayer: false,
                    isRevealed: false,
                    isMarked: false
                };
            }
        }
        this.enableTouchForExistingGrids();
    };
    /**
     * 为现有格子启用触摸事件
     */
    SinglePlayerController.prototype.enableTouchForExistingGrids = function () {
        var _this = this;
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 遍历棋盘的所有子节点，为格子添加触摸事件
        this.boardNode.children.forEach(function (child, index) {
            // 根据子节点的索引计算格子坐标
            var x = index % cols;
            var y = Math.floor(index / cols);
            if (x < cols && y < rows) {
                _this.addTouchEventToGrid(child, x, y);
                _this.gridNodes[x][y] = child;
            }
        });
    };
    /**
     * 为格子添加触摸事件
     */
    SinglePlayerController.prototype.addTouchEventToGrid = function (gridNode, x, y) {
        var _this = this;
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (_event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
    };
    /**
     * 格子点击事件 - 发送挖掘操作
     */
    SinglePlayerController.prototype.onGridClick = function (x, y) {
        console.log("\u683C\u5B50\u70B9\u51FB: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经被揭开或标记
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed || gridData.hasPlayer) {
            return;
        }
        // 发送挖掘操作到后端
        this.sendLevelClickBlock(x, y, 1);
    };
    /**
     * 格子长按事件 - 发送标记操作
     */
    SinglePlayerController.prototype.onGridLongPress = function (x, y) {
        console.log("\u683C\u5B50\u957F\u6309: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经被揭开
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed) {
            return;
        }
        // 发送标记操作到后端
        this.sendLevelClickBlock(x, y, 2);
    };
    /**
     * 发送关卡点击方块消息
     */
    SinglePlayerController.prototype.sendLevelClickBlock = function (x, y, action) {
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        console.log("\u53D1\u9001LevelClickBlock\u6D88\u606F:", clickData);
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    /**
     * 处理后端消息
     */
    SinglePlayerController.prototype.onReceiveMessage = function (data) {
        if (data.msgId === MessageId_1.MessageId.MsgTypeLevelClickBlock) {
            this.handleLevelClickBlockResponse(data.data);
        }
    };
    /**
     * 处理关卡点击方块响应
     */
    SinglePlayerController.prototype.handleLevelClickBlockResponse = function (responseData) {
        var _this = this;
        console.log("收到LevelClickBlock响应:", responseData);
        var x = responseData.x, y = responseData.y, result = responseData.result, action = responseData.action;
        // 更新格子状态
        if (this.isValidCoordinate(x, y)) {
            var gridData = this.gridData[x][y];
            if (action === 1) {
                // 挖掘操作 - 先隐藏格子，再显示结果
                gridData.isRevealed = true;
                gridData.hasPlayer = true;
                // 隐藏格子（模仿联机模式）
                this.removeGridAt(x, y);
                // 延迟显示结果（等格子消失动画完成）
                this.scheduleOnce(function () {
                    if (result === "mine") {
                        // 显示炸弹
                        _this.createBoomPrefab(x, y);
                    }
                    else if (typeof result === "number") {
                        // 显示数字
                        _this.createNumberPrefab(x, y, result);
                    }
                }, 0.3);
            }
            else if (action === 2) {
                // 标记操作
                if (result === "marked") {
                    // 添加标记
                    gridData.isMarked = true;
                    gridData.hasPlayer = true;
                    this.createBiaojiPrefab(x, y);
                }
                else if (result === "unmarked") {
                    // 取消标记
                    gridData.isMarked = false;
                    gridData.hasPlayer = false;
                    this.removePrefabAt(x, y);
                }
            }
        }
    };
    /**
     * 检查坐标是否有效
     */
    SinglePlayerController.prototype.isValidCoordinate = function (x, y) {
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        return x >= 0 && x < cols && y >= 0 && y < rows;
    };
    /**
     * 隐藏指定位置的格子（模仿联机模式的removeGridAt方法）
     */
    SinglePlayerController.prototype.removeGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子（模仿联机模式）
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false; // 隐藏而不是销毁
            })
                .start();
        }
    };
    /**
     * 计算预制体的精确位置（根据不同地图大小使用不同的坐标计算方式）
     */
    SinglePlayerController.prototype.calculatePrefabPosition = function (x, y) {
        // 方法1: 尝试使用格子节点的位置（推荐方式）
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            var gridPos = gridNode.getPosition();
            // 使用格子中心位置，稍微向下偏移一点
            return cc.v2(gridPos.x, gridPos.y - 16);
        }
        // 方法2: 如果没有格子节点，使用数学计算
        var _a = this.currentMapConfig, boardSize = _a.boardSize, gridSize = _a.gridSize;
        // 计算起始位置（左下角）
        var startX = -(boardSize.width / 2) + (gridSize.width / 2);
        var startY = -(boardSize.height / 2) + (gridSize.height / 2);
        // 计算最终位置
        var finalX = startX + (x * gridSize.width);
        var finalY = startY + (y * gridSize.height) - 16; // 向下偏移16像素
        return cc.v2(finalX, finalY);
    };
    /**
     * 在指定位置创建炸弹预制体
     */
    SinglePlayerController.prototype.createBoomPrefab = function (x, y) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置");
            return;
        }
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
    };
    /**
     * 在指定位置创建标记预制体
     */
    SinglePlayerController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置");
            return;
        }
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建数字预制体
     */
    SinglePlayerController.prototype.createNumberPrefab = function (x, y, number) {
        if (number === 0) {
            return; // 0不需要显示
        }
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 移除指定位置的预制体
     */
    SinglePlayerController.prototype.removePrefabAt = function (x, y) {
        // 查找并移除该位置的预制体
        var position = this.calculatePrefabPosition(x, y);
        var tolerance = 10; // 位置容差
        this.boardNode.children.forEach(function (child) {
            var childPos = child.getPosition();
            if (Math.abs(childPos.x - position.x) < tolerance &&
                Math.abs(childPos.y - position.y) < tolerance) {
                if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                    child.removeFromParent();
                }
            }
        });
    };
    /**
     * 清空所有预制体
     */
    SinglePlayerController.prototype.clearAllPrefabs = function () {
        this.boardNode.children.forEach(function (child) {
            if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                child.removeFromParent();
            }
        });
        // 重置格子数据
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        for (var x = 0; x < cols; x++) {
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y].hasPlayer = false;
                this.gridData[x][y].isRevealed = false;
                this.gridData[x][y].isMarked = false;
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], SinglePlayerController.prototype, "boardNode", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom8Prefab", void 0);
    SinglePlayerController = __decorate([
        ccclass
    ], SinglePlayerController);
    return SinglePlayerController;
}(cc.Component));
exports.default = SinglePlayerController;

cc._RF.pop();