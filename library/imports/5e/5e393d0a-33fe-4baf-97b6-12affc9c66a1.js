"use strict";
cc._RF.push(module, '5e3930KM/5Lr5e2Eq/8nGah', 'LevelItemController');
// scripts/hall/Level/LevelItemController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelItemController = /** @class */ (function (_super) {
    __extends(LevelItemController, _super);
    function LevelItemController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSprite = null;
        _this.levelLabel = null;
        _this.levelButton = null;
        _this.levelNumber = 1;
        _this.levelStatus = LevelSelectController_1.LevelStatus.LOCKED;
        _this.isSelected = false;
        return _this;
    }
    LevelItemController.prototype.onLoad = function () {
        // 初始化组件引用
        if (!this.levelSprite) {
            this.levelSprite = this.getComponent(cc.Sprite);
        }
        if (!this.levelLabel) {
            this.levelLabel = this.getComponentInChildren(cc.Label);
        }
        if (!this.levelButton) {
            this.levelButton = this.getComponent(cc.Button);
        }
    };
    LevelItemController.prototype.start = function () {
        // 确保在start时更新外观
        this.updateAppearance();
    };
    /**
     * 设置关卡数据
     */
    LevelItemController.prototype.setLevelData = function (levelNumber, status, selected) {
        if (selected === void 0) { selected = false; }
        this.levelNumber = levelNumber;
        this.levelStatus = status;
        this.isSelected = selected;
        // 更新标签文本
        if (this.levelLabel) {
            this.levelLabel.string = levelNumber.toString();
        }
        this.updateAppearance();
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelItemController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新外观
     */
    LevelItemController.prototype.updateAppearance = function () {
        var _this = this;
        if (!this.levelSprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(this.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径和大小
        if (this.isSelected) {
            size = cc.size(86, 86);
            switch (this.levelStatus) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (this.levelStatus) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        this.node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame && _this.levelSprite) {
                _this.levelSprite.spriteFrame = spriteFrame;
            }
            else if (err) {
                cc.error("Failed to load sprite: " + imagePath, err);
            }
        });
        // 设置按钮交互状态
        if (this.levelButton) {
            this.levelButton.interactable = (this.levelStatus !== LevelSelectController_1.LevelStatus.LOCKED);
        }
        // 设置标签样式
        this.setupLabelStyle();
    };
    /**
     * 设置选中状态
     */
    LevelItemController.prototype.setSelected = function (selected) {
        if (this.isSelected !== selected) {
            this.isSelected = selected;
            this.updateAppearance();
        }
    };
    /**
     * 获取关卡号
     */
    LevelItemController.prototype.getLevelNumber = function () {
        return this.levelNumber;
    };
    /**
     * 获取关卡状态
     */
    LevelItemController.prototype.getLevelStatus = function () {
        return this.levelStatus;
    };
    /**
     * 是否选中
     */
    LevelItemController.prototype.getIsSelected = function () {
        return this.isSelected;
    };
    /**
     * 设置标签样式
     */
    LevelItemController.prototype.setupLabelStyle = function () {
        if (!this.levelLabel) {
            cc.warn("LevelItemController: levelLabel is null");
            return;
        }
        // 设置字体大小为30
        this.levelLabel.fontSize = 30;
        // 设置颜色为白色 #FFFFFF
        this.levelLabel.node.color = cc.color(255, 255, 255);
        // 设置居中对齐
        this.levelLabel.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        this.levelLabel.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 注意：Cocos Creator的Label组件不支持直接设置加粗
        // 如需加粗效果，需要使用加粗字体文件
        // 添加外边框 LabelOutline
        var outline = this.levelLabel.getComponent(cc.LabelOutline);
        if (!outline) {
            outline = this.levelLabel.addComponent(cc.LabelOutline);
        }
        // 根据关卡状态设置边框颜色
        var outlineColor;
        switch (this.levelStatus) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
        // 确保标签位置居中
        this.levelLabel.node.setPosition(0, 0);
    };
    /**
     * 关卡点击事件
     */
    LevelItemController.prototype.onLevelClick = function () {
        if (this.levelStatus === LevelSelectController_1.LevelStatus.LOCKED) {
            return;
        }
        // 发送关卡选择事件
        this.node.emit('level-selected', this.levelNumber);
    };
    __decorate([
        property(cc.Sprite)
    ], LevelItemController.prototype, "levelSprite", void 0);
    __decorate([
        property(cc.Label)
    ], LevelItemController.prototype, "levelLabel", void 0);
    __decorate([
        property(cc.Button)
    ], LevelItemController.prototype, "levelButton", void 0);
    LevelItemController = __decorate([
        ccclass
    ], LevelItemController);
    return LevelItemController;
}(cc.Component));
exports.default = LevelItemController;

cc._RF.pop();