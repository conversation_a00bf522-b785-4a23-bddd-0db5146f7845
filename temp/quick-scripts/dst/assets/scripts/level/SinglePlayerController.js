
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/SinglePlayerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '882ddIZzIlMnadRsMiIVVUr', 'SinglePlayerController');
// scripts/level/SinglePlayerController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SinglePlayerController = /** @class */ (function (_super) {
    __extends(SinglePlayerController, _super);
    function SinglePlayerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardNode = null; // 棋盘节点
        _this.boomPrefab = null; // 炸弹预制体
        _this.biaojiPrefab = null; // 标记预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 地图配置
        _this.mapConfigs = {
            "8x8": {
                boardSize: { width: 752, height: 845 },
                gridSize: { width: 88, height: 88 },
                boardDimensions: { rows: 8, cols: 8 }
            },
            "9x9": {
                boardSize: { width: 752, height: 747 },
                gridSize: { width: 76, height: 76 },
                boardDimensions: { rows: 9, cols: 9 }
            },
            "9x10": {
                boardSize: { width: 752, height: 830 },
                gridSize: { width: 78, height: 78 },
                boardDimensions: { rows: 9, cols: 10 }
            },
            "10x10": {
                boardSize: { width: 752, height: 745 },
                gridSize: { width: 69, height: 69 },
                boardDimensions: { rows: 10, cols: 10 }
            }
        };
        // 当前地图配置
        _this.currentMapConfig = null;
        _this.currentMapType = "8x8"; // 默认8x8
        // 格子数据存储
        _this.gridData = [];
        _this.gridNodes = [];
        return _this;
    }
    SinglePlayerController.prototype.onLoad = function () {
        // 注册消息监听
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
        // 设置默认地图配置
        this.setMapType(this.currentMapType);
        this.initBoard();
    };
    SinglePlayerController.prototype.onDestroy = function () {
        // 取消消息监听
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 设置地图类型
     * @param mapType 地图类型 "8x8", "9x9", "9x10", "10x10"
     */
    SinglePlayerController.prototype.setMapType = function (mapType) {
        if (this.mapConfigs[mapType]) {
            this.currentMapType = mapType;
            this.currentMapConfig = this.mapConfigs[mapType];
            console.log("\u8BBE\u7F6E\u5730\u56FE\u7C7B\u578B\u4E3A: " + mapType, this.currentMapConfig);
            // 重新初始化棋盘
            if (this.boardNode) {
                this.initBoard();
            }
        }
        else {
            console.error("\u4E0D\u652F\u6301\u7684\u5730\u56FE\u7C7B\u578B: " + mapType);
        }
    };
    /**
     * 设置棋盘节点
     * @param boardNode 棋盘节点
     */
    SinglePlayerController.prototype.setBoardNode = function (boardNode) {
        this.boardNode = boardNode;
        if (this.currentMapConfig) {
            this.initBoard();
        }
    };
    /**
     * 初始化棋盘
     */
    SinglePlayerController.prototype.initBoard = function () {
        if (!this.currentMapConfig || !this.boardNode) {
            console.error("地图配置或棋盘节点未设置");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 初始化格子数据
        this.gridData = [];
        this.gridNodes = [];
        for (var x = 0; x < cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    hasPlayer: false,
                    isRevealed: false,
                    isMarked: false
                };
            }
        }
        this.enableTouchForExistingGrids();
    };
    /**
     * 为现有格子启用触摸事件
     */
    SinglePlayerController.prototype.enableTouchForExistingGrids = function () {
        var _this = this;
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 遍历棋盘的所有子节点，为格子添加触摸事件
        this.boardNode.children.forEach(function (child, index) {
            // 根据子节点的索引计算格子坐标
            var x = index % cols;
            var y = Math.floor(index / cols);
            if (x < cols && y < rows) {
                _this.addTouchEventToGrid(child, x, y);
                _this.gridNodes[x][y] = child;
            }
        });
    };
    /**
     * 为格子添加触摸事件
     */
    SinglePlayerController.prototype.addTouchEventToGrid = function (gridNode, x, y) {
        var _this = this;
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (_event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
    };
    /**
     * 格子点击事件 - 发送挖掘操作
     */
    SinglePlayerController.prototype.onGridClick = function (x, y) {
        console.log("\u683C\u5B50\u70B9\u51FB: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经被揭开或标记
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed || gridData.hasPlayer) {
            return;
        }
        // 发送挖掘操作到后端
        this.sendLevelClickBlock(x, y, 1);
    };
    /**
     * 格子长按事件 - 发送标记操作
     */
    SinglePlayerController.prototype.onGridLongPress = function (x, y) {
        console.log("\u683C\u5B50\u957F\u6309: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经被揭开
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed) {
            return;
        }
        // 发送标记操作到后端
        this.sendLevelClickBlock(x, y, 2);
    };
    /**
     * 发送关卡点击方块消息
     */
    SinglePlayerController.prototype.sendLevelClickBlock = function (x, y, action) {
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        console.log("\u53D1\u9001LevelClickBlock\u6D88\u606F:", clickData);
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    /**
     * 处理后端消息
     */
    SinglePlayerController.prototype.onReceiveMessage = function (data) {
        if (data.msgId === MessageId_1.MessageId.MsgTypeLevelClickBlock) {
            this.handleLevelClickBlockResponse(data.data);
        }
    };
    /**
     * 处理关卡点击方块响应
     */
    SinglePlayerController.prototype.handleLevelClickBlockResponse = function (responseData) {
        var _this = this;
        console.log("收到LevelClickBlock响应:", responseData);
        var x = responseData.x, y = responseData.y, result = responseData.result, action = responseData.action;
        // 更新格子状态
        if (this.isValidCoordinate(x, y)) {
            var gridData = this.gridData[x][y];
            if (action === 1) {
                // 挖掘操作 - 先隐藏格子，再显示结果
                gridData.isRevealed = true;
                gridData.hasPlayer = true;
                // 隐藏格子（模仿联机模式）
                this.removeGridAt(x, y);
                // 延迟显示结果（等格子消失动画完成）
                this.scheduleOnce(function () {
                    if (result === "mine") {
                        // 显示炸弹
                        _this.createBoomPrefab(x, y);
                    }
                    else if (typeof result === "number") {
                        // 显示数字
                        _this.createNumberPrefab(x, y, result);
                    }
                }, 0.3);
            }
            else if (action === 2) {
                // 标记操作
                if (result === "marked") {
                    // 添加标记
                    gridData.isMarked = true;
                    gridData.hasPlayer = true;
                    this.createBiaojiPrefab(x, y);
                }
                else if (result === "unmarked") {
                    // 取消标记
                    gridData.isMarked = false;
                    gridData.hasPlayer = false;
                    this.removePrefabAt(x, y);
                }
            }
        }
    };
    /**
     * 检查坐标是否有效
     */
    SinglePlayerController.prototype.isValidCoordinate = function (x, y) {
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        return x >= 0 && x < cols && y >= 0 && y < rows;
    };
    /**
     * 隐藏指定位置的格子（模仿联机模式的removeGridAt方法）
     */
    SinglePlayerController.prototype.removeGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子（模仿联机模式）
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false; // 隐藏而不是销毁
            })
                .start();
        }
    };
    /**
     * 计算预制体的精确位置（根据不同地图大小使用不同的坐标计算方式）
     */
    SinglePlayerController.prototype.calculatePrefabPosition = function (x, y) {
        // 方法1: 尝试使用格子节点的位置（推荐方式）
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            var gridPos = gridNode.getPosition();
            // 使用格子中心位置，稍微向下偏移一点
            return cc.v2(gridPos.x, gridPos.y - 16);
        }
        // 方法2: 如果没有格子节点，使用数学计算
        var _a = this.currentMapConfig, boardSize = _a.boardSize, gridSize = _a.gridSize;
        // 计算起始位置（左下角）
        var startX = -(boardSize.width / 2) + (gridSize.width / 2);
        var startY = -(boardSize.height / 2) + (gridSize.height / 2);
        // 计算最终位置
        var finalX = startX + (x * gridSize.width);
        var finalY = startY + (y * gridSize.height) - 16; // 向下偏移16像素
        return cc.v2(finalX, finalY);
    };
    /**
     * 在指定位置创建炸弹预制体
     */
    SinglePlayerController.prototype.createBoomPrefab = function (x, y) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置");
            return;
        }
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
    };
    /**
     * 在指定位置创建标记预制体
     */
    SinglePlayerController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置");
            return;
        }
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建数字预制体
     */
    SinglePlayerController.prototype.createNumberPrefab = function (x, y, number) {
        if (number === 0) {
            return; // 0不需要显示
        }
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 移除指定位置的预制体
     */
    SinglePlayerController.prototype.removePrefabAt = function (x, y) {
        // 查找并移除该位置的预制体
        var position = this.calculatePrefabPosition(x, y);
        var tolerance = 10; // 位置容差
        this.boardNode.children.forEach(function (child) {
            var childPos = child.getPosition();
            if (Math.abs(childPos.x - position.x) < tolerance &&
                Math.abs(childPos.y - position.y) < tolerance) {
                if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                    child.removeFromParent();
                }
            }
        });
    };
    /**
     * 清空所有预制体
     */
    SinglePlayerController.prototype.clearAllPrefabs = function () {
        this.boardNode.children.forEach(function (child) {
            if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                child.removeFromParent();
            }
        });
        // 重置格子数据
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        for (var x = 0; x < cols; x++) {
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y].hasPlayer = false;
                this.gridData[x][y].isRevealed = false;
                this.gridData[x][y].isMarked = false;
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], SinglePlayerController.prototype, "boardNode", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom8Prefab", void 0);
    SinglePlayerController = __decorate([
        ccclass
    ], SinglePlayerController);
    return SinglePlayerController;
}(cc.Component));
exports.default = SinglePlayerController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2xldmVsL1NpbmdsZVBsYXllckNvbnRyb2xsZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsNERBQTJEO0FBQzNELDhDQUE2QztBQUU3QyxxREFBa0Q7QUFDbEQsNkNBQTRDO0FBRXRDLElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBbUI1QztJQUFvRCwwQ0FBWTtJQUFoRTtRQUFBLHFFQWlnQkM7UUE5ZkcsZUFBUyxHQUFZLElBQUksQ0FBQyxDQUFDLE9BQU87UUFHbEMsZ0JBQVUsR0FBYyxJQUFJLENBQUMsQ0FBQyxRQUFRO1FBR3RDLGtCQUFZLEdBQWMsSUFBSSxDQUFDLENBQUMsUUFBUTtRQUd4QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFFeEMsaUJBQVcsR0FBYyxJQUFJLENBQUMsQ0FBQyxTQUFTO1FBRXhDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUMsU0FBUztRQUV4QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFFeEMsaUJBQVcsR0FBYyxJQUFJLENBQUMsQ0FBQyxTQUFTO1FBRXhDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUMsU0FBUztRQUV4QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFFeEMsaUJBQVcsR0FBYyxJQUFJLENBQUMsQ0FBQyxTQUFTO1FBRXhDLE9BQU87UUFDQyxnQkFBVSxHQUFpQztZQUMvQyxLQUFLLEVBQUU7Z0JBQ0gsU0FBUyxFQUFFLEVBQUUsS0FBSyxFQUFFLEdBQUcsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFO2dCQUN0QyxRQUFRLEVBQUUsRUFBRSxLQUFLLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxFQUFFLEVBQUU7Z0JBQ25DLGVBQWUsRUFBRSxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRTthQUN4QztZQUNELEtBQUssRUFBRTtnQkFDSCxTQUFTLEVBQUUsRUFBRSxLQUFLLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUU7Z0JBQ3RDLFFBQVEsRUFBRSxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLEVBQUUsRUFBRTtnQkFDbkMsZUFBZSxFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFO2FBQ3hDO1lBQ0QsTUFBTSxFQUFFO2dCQUNKLFNBQVMsRUFBRSxFQUFFLEtBQUssRUFBRSxHQUFHLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRTtnQkFDdEMsUUFBUSxFQUFFLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUUsRUFBRSxFQUFFO2dCQUNuQyxlQUFlLEVBQUUsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxFQUFFLEVBQUU7YUFDekM7WUFDRCxPQUFPLEVBQUU7Z0JBQ0wsU0FBUyxFQUFFLEVBQUUsS0FBSyxFQUFFLEdBQUcsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFO2dCQUN0QyxRQUFRLEVBQUUsRUFBRSxLQUFLLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxFQUFFLEVBQUU7Z0JBQ25DLGVBQWUsRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRTthQUMxQztTQUNKLENBQUM7UUFFRixTQUFTO1FBQ0Qsc0JBQWdCLEdBQWMsSUFBSSxDQUFDO1FBQ25DLG9CQUFjLEdBQVcsS0FBSyxDQUFDLENBQUMsUUFBUTtRQUVoRCxTQUFTO1FBQ0QsY0FBUSxHQUFpQixFQUFFLENBQUM7UUFDNUIsZUFBUyxHQUFnQixFQUFFLENBQUM7O0lBdWN4QyxDQUFDO0lBcmNHLHVDQUFNLEdBQU47UUFDSSxTQUFTO1FBQ1QsaUJBQU8sQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsdUJBQVMsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxDQUFDO1FBRXRGLFdBQVc7UUFDWCxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQztRQUNyQyxJQUFJLENBQUMsU0FBUyxFQUFFLENBQUM7SUFDckIsQ0FBQztJQUVELDBDQUFTLEdBQVQ7UUFDSSxTQUFTO1FBQ1QsaUJBQU8sQ0FBQyxLQUFLLENBQUMsbUJBQW1CLENBQUMsdUJBQVMsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxDQUFDO0lBQzdGLENBQUM7SUFFRDs7O09BR0c7SUFDSSwyQ0FBVSxHQUFqQixVQUFrQixPQUFlO1FBQzdCLElBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsRUFBRTtZQUMxQixJQUFJLENBQUMsY0FBYyxHQUFHLE9BQU8sQ0FBQztZQUM5QixJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUNqRCxPQUFPLENBQUMsR0FBRyxDQUFDLGlEQUFZLE9BQVMsRUFBRSxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztZQUUxRCxVQUFVO1lBQ1YsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO2dCQUNoQixJQUFJLENBQUMsU0FBUyxFQUFFLENBQUM7YUFDcEI7U0FDSjthQUFNO1lBQ0gsT0FBTyxDQUFDLEtBQUssQ0FBQyx1REFBYSxPQUFTLENBQUMsQ0FBQztTQUN6QztJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSSw2Q0FBWSxHQUFuQixVQUFvQixTQUFrQjtRQUNsQyxJQUFJLENBQUMsU0FBUyxHQUFHLFNBQVMsQ0FBQztRQUMzQixJQUFJLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUN2QixJQUFJLENBQUMsU0FBUyxFQUFFLENBQUM7U0FDcEI7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSywwQ0FBUyxHQUFqQjtRQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLElBQUksQ0FBQyxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQzNDLE9BQU8sQ0FBQyxLQUFLLENBQUMsY0FBYyxDQUFDLENBQUM7WUFDOUIsT0FBTztTQUNWO1FBRUssSUFBQSxLQUFpQixJQUFJLENBQUMsZ0JBQWdCLENBQUMsZUFBZSxFQUFwRCxJQUFJLFVBQUEsRUFBRSxJQUFJLFVBQTBDLENBQUM7UUFFN0QsVUFBVTtRQUNWLElBQUksQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDO1FBQ25CLElBQUksQ0FBQyxTQUFTLEdBQUcsRUFBRSxDQUFDO1FBRXBCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDM0IsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUM7WUFDdEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUM7WUFDdkIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRTtnQkFDM0IsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRztvQkFDbEIsQ0FBQyxFQUFFLENBQUM7b0JBQ0osQ0FBQyxFQUFFLENBQUM7b0JBQ0osU0FBUyxFQUFFLEtBQUs7b0JBQ2hCLFVBQVUsRUFBRSxLQUFLO29CQUNqQixRQUFRLEVBQUUsS0FBSztpQkFDbEIsQ0FBQzthQUNMO1NBQ0o7UUFFRCxJQUFJLENBQUMsMkJBQTJCLEVBQUUsQ0FBQztJQUN2QyxDQUFDO0lBRUQ7O09BRUc7SUFDSyw0REFBMkIsR0FBbkM7UUFBQSxpQkFtQkM7UUFsQkcsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUU7WUFDakIsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1lBQ25DLE9BQU87U0FDVjtRQUVLLElBQUEsS0FBaUIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLGVBQWUsRUFBcEQsSUFBSSxVQUFBLEVBQUUsSUFBSSxVQUEwQyxDQUFDO1FBRTdELHVCQUF1QjtRQUN2QixJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQyxLQUFjLEVBQUUsS0FBYTtZQUMxRCxpQkFBaUI7WUFDakIsSUFBTSxDQUFDLEdBQUcsS0FBSyxHQUFHLElBQUksQ0FBQztZQUN2QixJQUFNLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsQ0FBQztZQUVuQyxJQUFJLENBQUMsR0FBRyxJQUFJLElBQUksQ0FBQyxHQUFHLElBQUksRUFBRTtnQkFDdEIsS0FBSSxDQUFDLG1CQUFtQixDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBQ3RDLEtBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDO2FBQ2hDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxvREFBbUIsR0FBM0IsVUFBNEIsUUFBaUIsRUFBRSxDQUFTLEVBQUUsQ0FBUztRQUFuRSxpQkFnREM7UUEvQ0csU0FBUztRQUNULElBQUksY0FBYyxHQUFHLEtBQUssQ0FBQztRQUMzQixJQUFJLGNBQWMsR0FBRyxDQUFDLENBQUM7UUFDdkIsSUFBSSxpQkFBaUIsR0FBYSxJQUFJLENBQUM7UUFDdkMsSUFBTSxlQUFlLEdBQUcsR0FBRyxDQUFDLENBQUMsU0FBUztRQUV0QyxTQUFTO1FBQ1QsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxXQUFXLEVBQUUsVUFBQyxNQUEyQjtZQUNuRSxjQUFjLEdBQUcsSUFBSSxDQUFDO1lBQ3RCLGNBQWMsR0FBRyxDQUFDLENBQUM7WUFFbkIsU0FBUztZQUNULGlCQUFpQixHQUFHO2dCQUNoQixJQUFJLGNBQWMsRUFBRTtvQkFDaEIsY0FBYyxJQUFJLEdBQUcsQ0FBQztvQkFDdEIsSUFBSSxjQUFjLElBQUksZUFBZSxFQUFFO3dCQUNuQyxLQUFJLENBQUMsZUFBZSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQzt3QkFDM0IsY0FBYyxHQUFHLEtBQUssQ0FBQzt3QkFDdkIsSUFBSSxpQkFBaUIsRUFBRTs0QkFDbkIsS0FBSSxDQUFDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO3lCQUN0QztxQkFDSjtpQkFDSjtZQUNMLENBQUMsQ0FBQztZQUNGLEtBQUksQ0FBQyxRQUFRLENBQUMsaUJBQWlCLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFDMUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBRVQsU0FBUztRQUNULFFBQVEsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxFQUFFLFVBQUMsTUFBMkI7WUFDakUsaUJBQWlCO1lBQ2pCLElBQUksY0FBYyxJQUFJLGNBQWMsR0FBRyxlQUFlLEVBQUU7Z0JBQ3BELEtBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO2FBQzFCO1lBRUQsY0FBYyxHQUFHLEtBQUssQ0FBQztZQUN2QixJQUFJLGlCQUFpQixFQUFFO2dCQUNuQixLQUFJLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLENBQUM7YUFDdEM7UUFDTCxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFVCxTQUFTO1FBQ1QsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxZQUFZLEVBQUUsVUFBQyxNQUEyQjtZQUNwRSxjQUFjLEdBQUcsS0FBSyxDQUFDO1lBQ3ZCLElBQUksaUJBQWlCLEVBQUU7Z0JBQ25CLEtBQUksQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQUMsQ0FBQzthQUN0QztRQUNMLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUNiLENBQUM7SUFFRDs7T0FFRztJQUNLLDRDQUFXLEdBQW5CLFVBQW9CLENBQVMsRUFBRSxDQUFTO1FBQ3BDLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0NBQVUsQ0FBQyxVQUFLLENBQUMsTUFBRyxDQUFDLENBQUM7UUFFbEMsV0FBVztRQUNYLElBQUksQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1lBQy9CLE9BQU87U0FDVjtRQUVELGtCQUFrQjtRQUNsQixJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JDLElBQUksUUFBUSxDQUFDLFVBQVUsSUFBSSxRQUFRLENBQUMsU0FBUyxFQUFFO1lBQzNDLE9BQU87U0FDVjtRQUVELFlBQVk7UUFDWixJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUN0QyxDQUFDO0lBRUQ7O09BRUc7SUFDSyxnREFBZSxHQUF2QixVQUF3QixDQUFTLEVBQUUsQ0FBUztRQUN4QyxPQUFPLENBQUMsR0FBRyxDQUFDLGdDQUFVLENBQUMsVUFBSyxDQUFDLE1BQUcsQ0FBQyxDQUFDO1FBRWxDLFdBQVc7UUFDWCxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUMvQixPQUFPO1NBQ1Y7UUFFRCxlQUFlO1FBQ2YsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNyQyxJQUFJLFFBQVEsQ0FBQyxVQUFVLEVBQUU7WUFDckIsT0FBTztTQUNWO1FBRUQsWUFBWTtRQUNaLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ3RDLENBQUM7SUFFRDs7T0FFRztJQUNLLG9EQUFtQixHQUEzQixVQUE0QixDQUFTLEVBQUUsQ0FBUyxFQUFFLE1BQWM7UUFDNUQsSUFBTSxTQUFTLEdBQXNCO1lBQ2pDLENBQUMsRUFBRSxDQUFDO1lBQ0osQ0FBQyxFQUFFLENBQUM7WUFDSixNQUFNLEVBQUUsTUFBTSxDQUFDLHFCQUFxQjtTQUN2QyxDQUFDO1FBRUYsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQ0FBc0IsRUFBRSxTQUFTLENBQUMsQ0FBQztRQUMvQyxtQ0FBZ0IsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMscUJBQVMsQ0FBQyxzQkFBc0IsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUN4RixDQUFDO0lBRUQ7O09BRUc7SUFDSyxpREFBZ0IsR0FBeEIsVUFBeUIsSUFBUztRQUM5QixJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUsscUJBQVMsQ0FBQyxzQkFBc0IsRUFBRTtZQUNqRCxJQUFJLENBQUMsNkJBQTZCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1NBQ2pEO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssOERBQTZCLEdBQXJDLFVBQXNDLFlBQXFDO1FBQTNFLGlCQTJDQztRQTFDRyxPQUFPLENBQUMsR0FBRyxDQUFDLHNCQUFzQixFQUFFLFlBQVksQ0FBQyxDQUFDO1FBRTFDLElBQUEsQ0FBQyxHQUF3QixZQUFZLEVBQXBDLEVBQUUsQ0FBQyxHQUFxQixZQUFZLEVBQWpDLEVBQUUsTUFBTSxHQUFhLFlBQVksT0FBekIsRUFBRSxNQUFNLEdBQUssWUFBWSxPQUFqQixDQUFrQjtRQUU5QyxTQUFTO1FBQ1QsSUFBSSxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1lBQzlCLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFckMsSUFBSSxNQUFNLEtBQUssQ0FBQyxFQUFFO2dCQUNkLHFCQUFxQjtnQkFDckIsUUFBUSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUM7Z0JBQzNCLFFBQVEsQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDO2dCQUUxQixlQUFlO2dCQUNmLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO2dCQUV4QixvQkFBb0I7Z0JBQ3BCLElBQUksQ0FBQyxZQUFZLENBQUM7b0JBQ2QsSUFBSSxNQUFNLEtBQUssTUFBTSxFQUFFO3dCQUNuQixPQUFPO3dCQUNQLEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7cUJBQy9CO3lCQUFNLElBQUksT0FBTyxNQUFNLEtBQUssUUFBUSxFQUFFO3dCQUNuQyxPQUFPO3dCQUNQLEtBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDO3FCQUN6QztnQkFDTCxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7YUFFWDtpQkFBTSxJQUFJLE1BQU0sS0FBSyxDQUFDLEVBQUU7Z0JBQ3JCLE9BQU87Z0JBQ1AsSUFBSSxNQUFNLEtBQUssUUFBUSxFQUFFO29CQUNyQixPQUFPO29CQUNQLFFBQVEsQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDO29CQUN6QixRQUFRLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQztvQkFDMUIsSUFBSSxDQUFDLGtCQUFrQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztpQkFDakM7cUJBQU0sSUFBSSxNQUFNLEtBQUssVUFBVSxFQUFFO29CQUM5QixPQUFPO29CQUNQLFFBQVEsQ0FBQyxRQUFRLEdBQUcsS0FBSyxDQUFDO29CQUMxQixRQUFRLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQztvQkFDM0IsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7aUJBQzdCO2FBQ0o7U0FDSjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLGtEQUFpQixHQUF6QixVQUEwQixDQUFTLEVBQUUsQ0FBUztRQUNwQyxJQUFBLEtBQWlCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxlQUFlLEVBQXBELElBQUksVUFBQSxFQUFFLElBQUksVUFBMEMsQ0FBQztRQUM3RCxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUM7SUFDcEQsQ0FBQztJQUVEOztPQUVHO0lBQ0ssNkNBQVksR0FBcEIsVUFBcUIsQ0FBUyxFQUFFLENBQVM7UUFDckMsSUFBSSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFDL0IsT0FBTyxDQUFDLElBQUksQ0FBQyw0REFBYSxDQUFDLFVBQUssQ0FBQyxrQkFBSyxDQUFDLENBQUM7WUFDeEMsT0FBTztTQUNWO1FBRUQsU0FBUztRQUNULElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMzRCxJQUFJLFFBQVEsRUFBRTtZQUNWLG1CQUFtQjtZQUNuQixFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQztpQkFDYixFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsT0FBTyxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsQ0FBQyxFQUFFLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsQ0FBQztpQkFDbkUsSUFBSSxDQUFDO2dCQUNGLFFBQVEsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDLENBQUMsVUFBVTtZQUN2QyxDQUFDLENBQUM7aUJBQ0QsS0FBSyxFQUFFLENBQUM7U0FDaEI7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyx3REFBdUIsR0FBL0IsVUFBZ0MsQ0FBUyxFQUFFLENBQVM7UUFDaEQseUJBQXlCO1FBQ3pCLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMzRCxJQUFJLFFBQVEsRUFBRTtZQUNWLElBQU0sT0FBTyxHQUFHLFFBQVEsQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUN2QyxvQkFBb0I7WUFDcEIsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEVBQUUsT0FBTyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQztTQUMzQztRQUVELHVCQUF1QjtRQUNqQixJQUFBLEtBQTBCLElBQUksQ0FBQyxnQkFBZ0IsRUFBN0MsU0FBUyxlQUFBLEVBQUUsUUFBUSxjQUEwQixDQUFDO1FBRXRELGNBQWM7UUFDZCxJQUFNLE1BQU0sR0FBRyxDQUFDLENBQUMsU0FBUyxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFDN0QsSUFBTSxNQUFNLEdBQUcsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBRS9ELFNBQVM7UUFDVCxJQUFNLE1BQU0sR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQzdDLElBQU0sTUFBTSxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxRQUFRLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsV0FBVztRQUUvRCxPQUFPLEVBQUUsQ0FBQyxFQUFFLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFDO0lBQ2pDLENBQUM7SUFFRDs7T0FFRztJQUNLLGlEQUFnQixHQUF4QixVQUF5QixDQUFTLEVBQUUsQ0FBUztRQUN6QyxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNsQixPQUFPLENBQUMsS0FBSyxDQUFDLG1CQUFtQixDQUFDLENBQUM7WUFDbkMsT0FBTztTQUNWO1FBRUQsSUFBTSxRQUFRLEdBQUcsRUFBRSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7UUFDakQsUUFBUSxDQUFDLElBQUksR0FBRyxNQUFNLENBQUM7UUFFdkIsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNwRCxRQUFRLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRS9CLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRWxDLFNBQVM7UUFDVCxRQUFRLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JCLEVBQUUsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDO2FBQ2IsRUFBRSxDQUFDLEdBQUcsRUFBRSxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLFNBQVMsRUFBRSxDQUFDO2FBQzVELEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQzthQUNyQyxLQUFLLEVBQUUsQ0FBQztJQUNqQixDQUFDO0lBRUQ7O09BRUc7SUFDSyxtREFBa0IsR0FBMUIsVUFBMkIsQ0FBUyxFQUFFLENBQVM7UUFDM0MsSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDcEIsT0FBTyxDQUFDLEtBQUssQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1lBQ3JDLE9BQU87U0FDVjtRQUVELElBQU0sVUFBVSxHQUFHLEVBQUUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO1FBQ3JELFVBQVUsQ0FBQyxJQUFJLEdBQUcsUUFBUSxDQUFDO1FBRTNCLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDcEQsVUFBVSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUVqQyxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUVwQyxTQUFTO1FBQ1QsVUFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QixFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQzthQUNmLEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQUUsQ0FBQzthQUM1RCxLQUFLLEVBQUUsQ0FBQztJQUNqQixDQUFDO0lBRUQ7O09BRUc7SUFDSyxtREFBa0IsR0FBMUIsVUFBMkIsQ0FBUyxFQUFFLENBQVMsRUFBRSxNQUFjO1FBQzNELElBQUksTUFBTSxLQUFLLENBQUMsRUFBRTtZQUNkLE9BQU8sQ0FBQyxTQUFTO1NBQ3BCO1FBRUQsSUFBSSxNQUFNLEdBQWMsSUFBSSxDQUFDO1FBQzdCLFFBQVEsTUFBTSxFQUFFO1lBQ1osS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekMsS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekMsS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekMsS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekMsS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekMsS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekMsS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekMsS0FBSyxDQUFDO2dCQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDO2dCQUFDLE1BQU07WUFDekM7Z0JBQ0ksT0FBTyxDQUFDLEtBQUssQ0FBQywyQ0FBVyxNQUFRLENBQUMsQ0FBQztnQkFDbkMsT0FBTztTQUNkO1FBRUQsSUFBSSxDQUFDLE1BQU0sRUFBRTtZQUNULE9BQU8sQ0FBQyxLQUFLLENBQUMsU0FBTyxNQUFNLGdEQUFlLENBQUMsQ0FBQztZQUM1QyxPQUFPO1NBQ1Y7UUFFRCxJQUFNLFVBQVUsR0FBRyxFQUFFLENBQUMsV0FBVyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQzFDLFVBQVUsQ0FBQyxJQUFJLEdBQUcsU0FBTyxNQUFRLENBQUM7UUFFbEMsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNwRCxVQUFVLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBRWpDLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBRXBDLFNBQVM7UUFDVCxVQUFVLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3ZCLEVBQUUsQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDO2FBQ2YsRUFBRSxDQUFDLEdBQUcsRUFBRSxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLFNBQVMsRUFBRSxDQUFDO2FBQzVELEtBQUssRUFBRSxDQUFDO0lBQ2pCLENBQUM7SUFFRDs7T0FFRztJQUNLLCtDQUFjLEdBQXRCLFVBQXVCLENBQVMsRUFBRSxDQUFTO1FBQ3ZDLGVBQWU7UUFDZixJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsdUJBQXVCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ3BELElBQU0sU0FBUyxHQUFHLEVBQUUsQ0FBQyxDQUFDLE9BQU87UUFFN0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUMsS0FBYztZQUMzQyxJQUFNLFFBQVEsR0FBRyxLQUFLLENBQUMsV0FBVyxFQUFFLENBQUM7WUFDckMsSUFBSSxJQUFJLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDLEdBQUcsUUFBUSxDQUFDLENBQUMsQ0FBQyxHQUFHLFNBQVM7Z0JBQzdDLElBQUksQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUMsR0FBRyxRQUFRLENBQUMsQ0FBQyxDQUFDLEdBQUcsU0FBUyxFQUFFO2dCQUMvQyxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxFQUFFO29CQUM5RCxLQUFLLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQztpQkFDNUI7YUFDSjtRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ1AsQ0FBQztJQUVEOztPQUVHO0lBQ0ksZ0RBQWUsR0FBdEI7UUFDSSxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQyxLQUFjO1lBQzNDLElBQUksS0FBSyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLElBQUksS0FBSyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLEVBQUU7Z0JBQzlELEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO2FBQzVCO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxTQUFTO1FBQ0gsSUFBQSxLQUFpQixJQUFJLENBQUMsZ0JBQWdCLENBQUMsZUFBZSxFQUFwRCxJQUFJLFVBQUEsRUFBRSxJQUFJLFVBQTBDLENBQUM7UUFDN0QsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUMzQixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFO2dCQUMzQixJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7Z0JBQ3RDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztnQkFDdkMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEdBQUcsS0FBSyxDQUFDO2FBQ3hDO1NBQ0o7SUFDTCxDQUFDO0lBN2ZEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7NkRBQ1E7SUFHMUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzs4REFDUztJQUc3QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDO2dFQUNXO0lBRy9CO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7K0RBQ1U7SUFFOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzsrREFDVTtJQUU5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOytEQUNVO0lBRTlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7K0RBQ1U7SUFFOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzsrREFDVTtJQUU5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOytEQUNVO0lBRTlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7K0RBQ1U7SUFFOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzsrREFDVTtJQTFCYixzQkFBc0I7UUFEMUMsT0FBTztPQUNhLHNCQUFzQixDQWlnQjFDO0lBQUQsNkJBQUM7Q0FqZ0JELEFBaWdCQyxDQWpnQm1ELEVBQUUsQ0FBQyxTQUFTLEdBaWdCL0Q7a0JBamdCb0Isc0JBQXNCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgV2ViU29ja2V0TWFuYWdlciB9IGZyb20gJy4uL25ldC9XZWJTb2NrZXRNYW5hZ2VyJztcbmltcG9ydCB7IE1lc3NhZ2VJZCB9IGZyb20gJy4uL25ldC9NZXNzYWdlSWQnO1xuaW1wb3J0IHsgQ2xpY2tCbG9ja1JlcXVlc3QsIExldmVsQ2xpY2tCbG9ja1Jlc3BvbnNlIH0gZnJvbSAnLi4vYmVhbi9HYW1lQmVhbic7XG5pbXBvcnQgeyBFdmVudFR5cGUgfSBmcm9tICcuLi9jb21tb24vRXZlbnRDZW50ZXInO1xuaW1wb3J0IHsgR2FtZU1nciB9IGZyb20gJy4uL2NvbW1vbi9HYW1lTWdyJztcblxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuLy8g5Zyw5Zu+6YWN572u5o6l5Y+jXG5pbnRlcmZhY2UgTWFwQ29uZmlnIHtcbiAgICBib2FyZFNpemU6IHsgd2lkdGg6IG51bWJlciwgaGVpZ2h0OiBudW1iZXIgfTsgIC8vIOaji+ebmOWkp+Wwj1xuICAgIGdyaWRTaXplOiB7IHdpZHRoOiBudW1iZXIsIGhlaWdodDogbnVtYmVyIH07ICAgLy8g5qC85a2Q5aSn5bCPXG4gICAgYm9hcmREaW1lbnNpb25zOiB7IHJvd3M6IG51bWJlciwgY29sczogbnVtYmVyIH07IC8vIOihjOWIl+aVsFxufVxuXG4vLyDmoLzlrZDmlbDmja7mjqXlj6NcbmludGVyZmFjZSBHcmlkRGF0YSB7XG4gICAgeDogbnVtYmVyO1xuICAgIHk6IG51bWJlcjtcbiAgICBoYXNQbGF5ZXI6IGJvb2xlYW47XG4gICAgaXNSZXZlYWxlZDogYm9vbGVhbjtcbiAgICBpc01hcmtlZDogYm9vbGVhbjtcbn1cblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFNpbmdsZVBsYXllckNvbnRyb2xsZXIgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgYm9hcmROb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8g5qOL55uY6IqC54K5XG5cbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb21QcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOeCuOW8uemihOWItuS9k1xuXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBiaWFvamlQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOagh+iusOmihOWItuS9k1xuXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tMVByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgLy8g5pWw5a2XMemihOWItuS9k1xuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbTJQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOaVsOWtlzLpooTliLbkvZNcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb20zUHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAvLyDmlbDlrZcz6aKE5Yi25L2TXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tNFByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgLy8g5pWw5a2XNOmihOWItuS9k1xuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbTVQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOaVsOWtlzXpooTliLbkvZNcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb202UHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAvLyDmlbDlrZc26aKE5Yi25L2TXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tN1ByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgLy8g5pWw5a2XN+mihOWItuS9k1xuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbThQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOaVsOWtlzjpooTliLbkvZNcblxuICAgIC8vIOWcsOWbvumFjee9rlxuICAgIHByaXZhdGUgbWFwQ29uZmlnczogeyBba2V5OiBzdHJpbmddOiBNYXBDb25maWcgfSA9IHtcbiAgICAgICAgXCI4eDhcIjoge1xuICAgICAgICAgICAgYm9hcmRTaXplOiB7IHdpZHRoOiA3NTIsIGhlaWdodDogODQ1IH0sXG4gICAgICAgICAgICBncmlkU2l6ZTogeyB3aWR0aDogODgsIGhlaWdodDogODggfSxcbiAgICAgICAgICAgIGJvYXJkRGltZW5zaW9uczogeyByb3dzOiA4LCBjb2xzOiA4IH1cbiAgICAgICAgfSxcbiAgICAgICAgXCI5eDlcIjoge1xuICAgICAgICAgICAgYm9hcmRTaXplOiB7IHdpZHRoOiA3NTIsIGhlaWdodDogNzQ3IH0sXG4gICAgICAgICAgICBncmlkU2l6ZTogeyB3aWR0aDogNzYsIGhlaWdodDogNzYgfSxcbiAgICAgICAgICAgIGJvYXJkRGltZW5zaW9uczogeyByb3dzOiA5LCBjb2xzOiA5IH1cbiAgICAgICAgfSxcbiAgICAgICAgXCI5eDEwXCI6IHtcbiAgICAgICAgICAgIGJvYXJkU2l6ZTogeyB3aWR0aDogNzUyLCBoZWlnaHQ6IDgzMCB9LFxuICAgICAgICAgICAgZ3JpZFNpemU6IHsgd2lkdGg6IDc4LCBoZWlnaHQ6IDc4IH0sXG4gICAgICAgICAgICBib2FyZERpbWVuc2lvbnM6IHsgcm93czogOSwgY29sczogMTAgfVxuICAgICAgICB9LFxuICAgICAgICBcIjEweDEwXCI6IHtcbiAgICAgICAgICAgIGJvYXJkU2l6ZTogeyB3aWR0aDogNzUyLCBoZWlnaHQ6IDc0NSB9LFxuICAgICAgICAgICAgZ3JpZFNpemU6IHsgd2lkdGg6IDY5LCBoZWlnaHQ6IDY5IH0sXG4gICAgICAgICAgICBib2FyZERpbWVuc2lvbnM6IHsgcm93czogMTAsIGNvbHM6IDEwIH1cbiAgICAgICAgfVxuICAgIH07XG5cbiAgICAvLyDlvZPliY3lnLDlm77phY3nva5cbiAgICBwcml2YXRlIGN1cnJlbnRNYXBDb25maWc6IE1hcENvbmZpZyA9IG51bGw7XG4gICAgcHJpdmF0ZSBjdXJyZW50TWFwVHlwZTogc3RyaW5nID0gXCI4eDhcIjsgLy8g6buY6K6kOHg4XG5cbiAgICAvLyDmoLzlrZDmlbDmja7lrZjlgqhcbiAgICBwcml2YXRlIGdyaWREYXRhOiBHcmlkRGF0YVtdW10gPSBbXTtcbiAgICBwcml2YXRlIGdyaWROb2RlczogY2MuTm9kZVtdW10gPSBbXTtcblxuICAgIG9uTG9hZCgpIHtcbiAgICAgICAgLy8g5rOo5YaM5raI5oGv55uR5ZCsXG4gICAgICAgIEdhbWVNZ3IuRXZlbnQuQWRkRXZlbnRMaXN0ZW5lcihFdmVudFR5cGUuUmVjZWl2ZU1lc3NhZ2UsIHRoaXMub25SZWNlaXZlTWVzc2FnZSwgdGhpcyk7XG5cbiAgICAgICAgLy8g6K6+572u6buY6K6k5Zyw5Zu+6YWN572uXG4gICAgICAgIHRoaXMuc2V0TWFwVHlwZSh0aGlzLmN1cnJlbnRNYXBUeXBlKTtcbiAgICAgICAgdGhpcy5pbml0Qm9hcmQoKTtcbiAgICB9XG5cbiAgICBvbkRlc3Ryb3koKSB7XG4gICAgICAgIC8vIOWPlua2iOa2iOaBr+ebkeWQrFxuICAgICAgICBHYW1lTWdyLkV2ZW50LlJlbW92ZUV2ZW50TGlzdGVuZXIoRXZlbnRUeXBlLlJlY2VpdmVNZXNzYWdlLCB0aGlzLm9uUmVjZWl2ZU1lc3NhZ2UsIHRoaXMpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiuvue9ruWcsOWbvuexu+Wei1xuICAgICAqIEBwYXJhbSBtYXBUeXBlIOWcsOWbvuexu+WeiyBcIjh4OFwiLCBcIjl4OVwiLCBcIjl4MTBcIiwgXCIxMHgxMFwiXG4gICAgICovXG4gICAgcHVibGljIHNldE1hcFR5cGUobWFwVHlwZTogc3RyaW5nKSB7XG4gICAgICAgIGlmICh0aGlzLm1hcENvbmZpZ3NbbWFwVHlwZV0pIHtcbiAgICAgICAgICAgIHRoaXMuY3VycmVudE1hcFR5cGUgPSBtYXBUeXBlO1xuICAgICAgICAgICAgdGhpcy5jdXJyZW50TWFwQ29uZmlnID0gdGhpcy5tYXBDb25maWdzW21hcFR5cGVdO1xuICAgICAgICAgICAgY29uc29sZS5sb2coYOiuvue9ruWcsOWbvuexu+Wei+S4ujogJHttYXBUeXBlfWAsIHRoaXMuY3VycmVudE1hcENvbmZpZyk7XG5cbiAgICAgICAgICAgIC8vIOmHjeaWsOWIneWni+WMluaji+ebmFxuICAgICAgICAgICAgaWYgKHRoaXMuYm9hcmROb2RlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5pbml0Qm9hcmQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOS4jeaUr+aMgeeahOWcsOWbvuexu+WeizogJHttYXBUeXBlfWApO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6K6+572u5qOL55uY6IqC54K5XG4gICAgICogQHBhcmFtIGJvYXJkTm9kZSDmo4vnm5joioLngrlcbiAgICAgKi9cbiAgICBwdWJsaWMgc2V0Qm9hcmROb2RlKGJvYXJkTm9kZTogY2MuTm9kZSkge1xuICAgICAgICB0aGlzLmJvYXJkTm9kZSA9IGJvYXJkTm9kZTtcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcENvbmZpZykge1xuICAgICAgICAgICAgdGhpcy5pbml0Qm9hcmQoKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWIneWni+WMluaji+ebmFxuICAgICAqL1xuICAgIHByaXZhdGUgaW5pdEJvYXJkKCkge1xuICAgICAgICBpZiAoIXRoaXMuY3VycmVudE1hcENvbmZpZyB8fCAhdGhpcy5ib2FyZE5vZGUpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCLlnLDlm77phY3nva7miJbmo4vnm5joioLngrnmnKrorr7nva5cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB7IHJvd3MsIGNvbHMgfSA9IHRoaXMuY3VycmVudE1hcENvbmZpZy5ib2FyZERpbWVuc2lvbnM7XG4gICAgICAgIFxuICAgICAgICAvLyDliJ3lp4vljJbmoLzlrZDmlbDmja5cbiAgICAgICAgdGhpcy5ncmlkRGF0YSA9IFtdO1xuICAgICAgICB0aGlzLmdyaWROb2RlcyA9IFtdO1xuICAgICAgICBcbiAgICAgICAgZm9yIChsZXQgeCA9IDA7IHggPCBjb2xzOyB4KyspIHtcbiAgICAgICAgICAgIHRoaXMuZ3JpZERhdGFbeF0gPSBbXTtcbiAgICAgICAgICAgIHRoaXMuZ3JpZE5vZGVzW3hdID0gW107XG4gICAgICAgICAgICBmb3IgKGxldCB5ID0gMDsgeSA8IHJvd3M7IHkrKykge1xuICAgICAgICAgICAgICAgIHRoaXMuZ3JpZERhdGFbeF1beV0gPSB7XG4gICAgICAgICAgICAgICAgICAgIHg6IHgsXG4gICAgICAgICAgICAgICAgICAgIHk6IHksXG4gICAgICAgICAgICAgICAgICAgIGhhc1BsYXllcjogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGlzUmV2ZWFsZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBpc01hcmtlZDogZmFsc2VcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgdGhpcy5lbmFibGVUb3VjaEZvckV4aXN0aW5nR3JpZHMoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDkuLrnjrDmnInmoLzlrZDlkK/nlKjop6bmkbjkuovku7ZcbiAgICAgKi9cbiAgICBwcml2YXRlIGVuYWJsZVRvdWNoRm9yRXhpc3RpbmdHcmlkcygpIHtcbiAgICAgICAgaWYgKCF0aGlzLmJvYXJkTm9kZSkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIuaji+ebmOiKgueCueacquiuvue9ru+8jOaXoOazleWQr+eUqOinpuaRuOS6i+S7tu+8gVwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHsgcm93cywgY29scyB9ID0gdGhpcy5jdXJyZW50TWFwQ29uZmlnLmJvYXJkRGltZW5zaW9ucztcblxuICAgICAgICAvLyDpgY3ljobmo4vnm5jnmoTmiYDmnInlrZDoioLngrnvvIzkuLrmoLzlrZDmt7vliqDop6bmkbjkuovku7ZcbiAgICAgICAgdGhpcy5ib2FyZE5vZGUuY2hpbGRyZW4uZm9yRWFjaCgoY2hpbGQ6IGNjLk5vZGUsIGluZGV4OiBudW1iZXIpID0+IHtcbiAgICAgICAgICAgIC8vIOagueaNruWtkOiKgueCueeahOe0ouW8leiuoeeul+agvOWtkOWdkOagh1xuICAgICAgICAgICAgY29uc3QgeCA9IGluZGV4ICUgY29scztcbiAgICAgICAgICAgIGNvbnN0IHkgPSBNYXRoLmZsb29yKGluZGV4IC8gY29scyk7XG5cbiAgICAgICAgICAgIGlmICh4IDwgY29scyAmJiB5IDwgcm93cykge1xuICAgICAgICAgICAgICAgIHRoaXMuYWRkVG91Y2hFdmVudFRvR3JpZChjaGlsZCwgeCwgeSk7XG4gICAgICAgICAgICAgICAgdGhpcy5ncmlkTm9kZXNbeF1beV0gPSBjaGlsZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Li65qC85a2Q5re75Yqg6Kem5pG45LqL5Lu2XG4gICAgICovXG4gICAgcHJpdmF0ZSBhZGRUb3VjaEV2ZW50VG9HcmlkKGdyaWROb2RlOiBjYy5Ob2RlLCB4OiBudW1iZXIsIHk6IG51bWJlcikge1xuICAgICAgICAvLyDplb/mjInnm7jlhbPlj5jph49cbiAgICAgICAgbGV0IGlzTG9uZ1ByZXNzaW5nID0gZmFsc2U7XG4gICAgICAgIGxldCBsb25nUHJlc3NUaW1lciA9IDA7XG4gICAgICAgIGxldCBsb25nUHJlc3NDYWxsYmFjazogRnVuY3Rpb24gPSBudWxsO1xuICAgICAgICBjb25zdCBMT05HX1BSRVNTX1RJTUUgPSAxLjA7IC8vIDHnp5Lplb/mjInml7bpl7RcblxuICAgICAgICAvLyDop6bmkbjlvIDlp4vkuovku7ZcbiAgICAgICAgZ3JpZE5vZGUub24oY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfU1RBUlQsIChfZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpID0+IHtcbiAgICAgICAgICAgIGlzTG9uZ1ByZXNzaW5nID0gdHJ1ZTtcbiAgICAgICAgICAgIGxvbmdQcmVzc1RpbWVyID0gMDtcblxuICAgICAgICAgICAgLy8g5byA5aeL6ZW/5oyJ5qOA5rWLXG4gICAgICAgICAgICBsb25nUHJlc3NDYWxsYmFjayA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoaXNMb25nUHJlc3NpbmcpIHtcbiAgICAgICAgICAgICAgICAgICAgbG9uZ1ByZXNzVGltZXIgKz0gMC4xO1xuICAgICAgICAgICAgICAgICAgICBpZiAobG9uZ1ByZXNzVGltZXIgPj0gTE9OR19QUkVTU19USU1FKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9uR3JpZExvbmdQcmVzcyh4LCB5KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzTG9uZ1ByZXNzaW5nID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAobG9uZ1ByZXNzQ2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnVuc2NoZWR1bGUobG9uZ1ByZXNzQ2FsbGJhY2spO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHRoaXMuc2NoZWR1bGUobG9uZ1ByZXNzQ2FsbGJhY2ssIDAuMSk7XG4gICAgICAgIH0sIHRoaXMpO1xuXG4gICAgICAgIC8vIOinpuaRuOe7k+adn+S6i+S7tlxuICAgICAgICBncmlkTm9kZS5vbihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9FTkQsIChfZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpID0+IHtcbiAgICAgICAgICAgIC8vIOWmguaenOS4jeaYr+mVv+aMie+8jOWImeaJp+ihjOeCueWHu+S6i+S7tlxuICAgICAgICAgICAgaWYgKGlzTG9uZ1ByZXNzaW5nICYmIGxvbmdQcmVzc1RpbWVyIDwgTE9OR19QUkVTU19USU1FKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5vbkdyaWRDbGljayh4LCB5KTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgaXNMb25nUHJlc3NpbmcgPSBmYWxzZTtcbiAgICAgICAgICAgIGlmIChsb25nUHJlc3NDYWxsYmFjaykge1xuICAgICAgICAgICAgICAgIHRoaXMudW5zY2hlZHVsZShsb25nUHJlc3NDYWxsYmFjayk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sIHRoaXMpO1xuXG4gICAgICAgIC8vIOinpuaRuOWPlua2iOS6i+S7tlxuICAgICAgICBncmlkTm9kZS5vbihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9DQU5DRUwsIChfZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpID0+IHtcbiAgICAgICAgICAgIGlzTG9uZ1ByZXNzaW5nID0gZmFsc2U7XG4gICAgICAgICAgICBpZiAobG9uZ1ByZXNzQ2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICB0aGlzLnVuc2NoZWR1bGUobG9uZ1ByZXNzQ2FsbGJhY2spO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCB0aGlzKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmoLzlrZDngrnlh7vkuovku7YgLSDlj5HpgIHmjJbmjpjmk43kvZxcbiAgICAgKi9cbiAgICBwcml2YXRlIG9uR3JpZENsaWNrKHg6IG51bWJlciwgeTogbnVtYmVyKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDmoLzlrZDngrnlh7s6ICgke3h9LCAke3l9KWApO1xuXG4gICAgICAgIC8vIOajgOafpeWdkOagh+aYr+WQpuacieaViFxuICAgICAgICBpZiAoIXRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOajgOafpeivpeS9jee9ruaYr+WQpuW3sue7j+iiq+aPreW8gOaIluagh+iusFxuICAgICAgICBjb25zdCBncmlkRGF0YSA9IHRoaXMuZ3JpZERhdGFbeF1beV07XG4gICAgICAgIGlmIChncmlkRGF0YS5pc1JldmVhbGVkIHx8IGdyaWREYXRhLmhhc1BsYXllcikge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5Y+R6YCB5oyW5o6Y5pON5L2c5Yiw5ZCO56uvXG4gICAgICAgIHRoaXMuc2VuZExldmVsQ2xpY2tCbG9jayh4LCB5LCAxKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmoLzlrZDplb/mjInkuovku7YgLSDlj5HpgIHmoIforrDmk43kvZxcbiAgICAgKi9cbiAgICBwcml2YXRlIG9uR3JpZExvbmdQcmVzcyh4OiBudW1iZXIsIHk6IG51bWJlcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg5qC85a2Q6ZW/5oyJOiAoJHt4fSwgJHt5fSlgKTtcblxuICAgICAgICAvLyDmo4Dmn6XlnZDmoIfmmK/lkKbmnInmlYhcbiAgICAgICAgaWYgKCF0aGlzLmlzVmFsaWRDb29yZGluYXRlKHgsIHkpKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmo4Dmn6Xor6XkvY3nva7mmK/lkKblt7Lnu4/ooqvmj63lvIBcbiAgICAgICAgY29uc3QgZ3JpZERhdGEgPSB0aGlzLmdyaWREYXRhW3hdW3ldO1xuICAgICAgICBpZiAoZ3JpZERhdGEuaXNSZXZlYWxlZCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5Y+R6YCB5qCH6K6w5pON5L2c5Yiw5ZCO56uvXG4gICAgICAgIHRoaXMuc2VuZExldmVsQ2xpY2tCbG9jayh4LCB5LCAyKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlj5HpgIHlhbPljaHngrnlh7vmlrnlnZfmtojmga9cbiAgICAgKi9cbiAgICBwcml2YXRlIHNlbmRMZXZlbENsaWNrQmxvY2soeDogbnVtYmVyLCB5OiBudW1iZXIsIGFjdGlvbjogbnVtYmVyKSB7XG4gICAgICAgIGNvbnN0IGNsaWNrRGF0YTogQ2xpY2tCbG9ja1JlcXVlc3QgPSB7XG4gICAgICAgICAgICB4OiB4LFxuICAgICAgICAgICAgeTogeSxcbiAgICAgICAgICAgIGFjdGlvbjogYWN0aW9uIC8vIDE95oyW5o6Y5pa55Z2X77yMMj3moIforrAv5Y+W5raI5qCH6K6w5Zyw6Zu3XG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc29sZS5sb2coYOWPkemAgUxldmVsQ2xpY2tCbG9ja+a2iOaBrzpgLCBjbGlja0RhdGEpO1xuICAgICAgICBXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkuc2VuZE1zZyhNZXNzYWdlSWQuTXNnVHlwZUxldmVsQ2xpY2tCbG9jaywgY2xpY2tEYXRhKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlpITnkIblkI7nq6/mtojmga9cbiAgICAgKi9cbiAgICBwcml2YXRlIG9uUmVjZWl2ZU1lc3NhZ2UoZGF0YTogYW55KSB7XG4gICAgICAgIGlmIChkYXRhLm1zZ0lkID09PSBNZXNzYWdlSWQuTXNnVHlwZUxldmVsQ2xpY2tCbG9jaykge1xuICAgICAgICAgICAgdGhpcy5oYW5kbGVMZXZlbENsaWNrQmxvY2tSZXNwb25zZShkYXRhLmRhdGEpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CG5YWz5Y2h54K55Ye75pa55Z2X5ZON5bqUXG4gICAgICovXG4gICAgcHJpdmF0ZSBoYW5kbGVMZXZlbENsaWNrQmxvY2tSZXNwb25zZShyZXNwb25zZURhdGE6IExldmVsQ2xpY2tCbG9ja1Jlc3BvbnNlKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwi5pS25YiwTGV2ZWxDbGlja0Jsb2Nr5ZON5bqUOlwiLCByZXNwb25zZURhdGEpO1xuXG4gICAgICAgIGNvbnN0IHsgeCwgeSwgcmVzdWx0LCBhY3Rpb24gfSA9IHJlc3BvbnNlRGF0YTtcblxuICAgICAgICAvLyDmm7TmlrDmoLzlrZDnirbmgIFcbiAgICAgICAgaWYgKHRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgIGNvbnN0IGdyaWREYXRhID0gdGhpcy5ncmlkRGF0YVt4XVt5XTtcblxuICAgICAgICAgICAgaWYgKGFjdGlvbiA9PT0gMSkge1xuICAgICAgICAgICAgICAgIC8vIOaMluaOmOaTjeS9nCAtIOWFiOmakOiXj+agvOWtkO+8jOWGjeaYvuekuue7k+aenFxuICAgICAgICAgICAgICAgIGdyaWREYXRhLmlzUmV2ZWFsZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgIGdyaWREYXRhLmhhc1BsYXllciA9IHRydWU7XG5cbiAgICAgICAgICAgICAgICAvLyDpmpDol4/moLzlrZDvvIjmqKHku7/ogZTmnLrmqKHlvI/vvIlcbiAgICAgICAgICAgICAgICB0aGlzLnJlbW92ZUdyaWRBdCh4LCB5KTtcblxuICAgICAgICAgICAgICAgIC8vIOW7tui/n+aYvuekuue7k+aenO+8iOetieagvOWtkOa2iOWkseWKqOeUu+WujOaIkO+8iVxuICAgICAgICAgICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3VsdCA9PT0gXCJtaW5lXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOaYvuekuueCuOW8uVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jcmVhdGVCb29tUHJlZmFiKHgsIHkpO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiByZXN1bHQgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOaYvuekuuaVsOWtl1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jcmVhdGVOdW1iZXJQcmVmYWIoeCwgeSwgcmVzdWx0KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIDAuMyk7XG5cbiAgICAgICAgICAgIH0gZWxzZSBpZiAoYWN0aW9uID09PSAyKSB7XG4gICAgICAgICAgICAgICAgLy8g5qCH6K6w5pON5L2cXG4gICAgICAgICAgICAgICAgaWYgKHJlc3VsdCA9PT0gXCJtYXJrZWRcIikge1xuICAgICAgICAgICAgICAgICAgICAvLyDmt7vliqDmoIforrBcbiAgICAgICAgICAgICAgICAgICAgZ3JpZERhdGEuaXNNYXJrZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICBncmlkRGF0YS5oYXNQbGF5ZXIgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmNyZWF0ZUJpYW9qaVByZWZhYih4LCB5KTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHJlc3VsdCA9PT0gXCJ1bm1hcmtlZFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOWPlua2iOagh+iusFxuICAgICAgICAgICAgICAgICAgICBncmlkRGF0YS5pc01hcmtlZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICBncmlkRGF0YS5oYXNQbGF5ZXIgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZW1vdmVQcmVmYWJBdCh4LCB5KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmo4Dmn6XlnZDmoIfmmK/lkKbmnInmlYhcbiAgICAgKi9cbiAgICBwcml2YXRlIGlzVmFsaWRDb29yZGluYXRlKHg6IG51bWJlciwgeTogbnVtYmVyKTogYm9vbGVhbiB7XG4gICAgICAgIGNvbnN0IHsgcm93cywgY29scyB9ID0gdGhpcy5jdXJyZW50TWFwQ29uZmlnLmJvYXJkRGltZW5zaW9ucztcbiAgICAgICAgcmV0dXJuIHggPj0gMCAmJiB4IDwgY29scyAmJiB5ID49IDAgJiYgeSA8IHJvd3M7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6ZqQ6JeP5oyH5a6a5L2N572u55qE5qC85a2Q77yI5qih5Lu/6IGU5py65qih5byP55qEcmVtb3ZlR3JpZEF05pa55rOV77yJXG4gICAgICovXG4gICAgcHJpdmF0ZSByZW1vdmVHcmlkQXQoeDogbnVtYmVyLCB5OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzVmFsaWRDb29yZGluYXRlKHgsIHkpKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYOmakOiXj+agvOWtkOWksei0pe+8muWdkOaghygke3h9LCAke3l9KeaXoOaViGApO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g6I635Y+W5qC85a2Q6IqC54K5XG4gICAgICAgIGNvbnN0IGdyaWROb2RlID0gdGhpcy5ncmlkTm9kZXNbeF0gJiYgdGhpcy5ncmlkTm9kZXNbeF1beV07XG4gICAgICAgIGlmIChncmlkTm9kZSkge1xuICAgICAgICAgICAgLy8g5L2/55So5Yqo55S76ZqQ6JeP5qC85a2Q77yI5qih5Lu/6IGU5py65qih5byP77yJXG4gICAgICAgICAgICBjYy50d2VlbihncmlkTm9kZSlcbiAgICAgICAgICAgICAgICAudG8oMC4zLCB7IG9wYWNpdHk6IDAsIHNjYWxlWDogMCwgc2NhbGVZOiAwIH0sIHsgZWFzaW5nOiAnc2luZUluJyB9KVxuICAgICAgICAgICAgICAgIC5jYWxsKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgZ3JpZE5vZGUuYWN0aXZlID0gZmFsc2U7IC8vIOmakOiXj+iAjOS4jeaYr+mUgOavgVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgLnN0YXJ0KCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDorqHnrpfpooTliLbkvZPnmoTnsr7noa7kvY3nva7vvIjmoLnmja7kuI3lkIzlnLDlm77lpKflsI/kvb/nlKjkuI3lkIznmoTlnZDmoIforqHnrpfmlrnlvI/vvIlcbiAgICAgKi9cbiAgICBwcml2YXRlIGNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uKHg6IG51bWJlciwgeTogbnVtYmVyKTogY2MuVmVjMiB7XG4gICAgICAgIC8vIOaWueazlTE6IOWwneivleS9v+eUqOagvOWtkOiKgueCueeahOS9jee9ru+8iOaOqOiNkOaWueW8j++8iVxuICAgICAgICBjb25zdCBncmlkTm9kZSA9IHRoaXMuZ3JpZE5vZGVzW3hdICYmIHRoaXMuZ3JpZE5vZGVzW3hdW3ldO1xuICAgICAgICBpZiAoZ3JpZE5vZGUpIHtcbiAgICAgICAgICAgIGNvbnN0IGdyaWRQb3MgPSBncmlkTm9kZS5nZXRQb3NpdGlvbigpO1xuICAgICAgICAgICAgLy8g5L2/55So5qC85a2Q5Lit5b+D5L2N572u77yM56iN5b6u5ZCR5LiL5YGP56e75LiA54K5XG4gICAgICAgICAgICByZXR1cm4gY2MudjIoZ3JpZFBvcy54LCBncmlkUG9zLnkgLSAxNik7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmlrnms5UyOiDlpoLmnpzmsqHmnInmoLzlrZDoioLngrnvvIzkvb/nlKjmlbDlraborqHnrpdcbiAgICAgICAgY29uc3QgeyBib2FyZFNpemUsIGdyaWRTaXplIH0gPSB0aGlzLmN1cnJlbnRNYXBDb25maWc7XG5cbiAgICAgICAgLy8g6K6h566X6LW35aeL5L2N572u77yI5bem5LiL6KeS77yJXG4gICAgICAgIGNvbnN0IHN0YXJ0WCA9IC0oYm9hcmRTaXplLndpZHRoIC8gMikgKyAoZ3JpZFNpemUud2lkdGggLyAyKTtcbiAgICAgICAgY29uc3Qgc3RhcnRZID0gLShib2FyZFNpemUuaGVpZ2h0IC8gMikgKyAoZ3JpZFNpemUuaGVpZ2h0IC8gMik7XG5cbiAgICAgICAgLy8g6K6h566X5pyA57uI5L2N572uXG4gICAgICAgIGNvbnN0IGZpbmFsWCA9IHN0YXJ0WCArICh4ICogZ3JpZFNpemUud2lkdGgpO1xuICAgICAgICBjb25zdCBmaW5hbFkgPSBzdGFydFkgKyAoeSAqIGdyaWRTaXplLmhlaWdodCkgLSAxNjsgLy8g5ZCR5LiL5YGP56e7MTblg4/ntKBcblxuICAgICAgICByZXR1cm4gY2MudjIoZmluYWxYLCBmaW5hbFkpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWcqOaMh+WumuS9jee9ruWIm+W7uueCuOW8uemihOWItuS9k1xuICAgICAqL1xuICAgIHByaXZhdGUgY3JlYXRlQm9vbVByZWZhYih4OiBudW1iZXIsIHk6IG51bWJlcikge1xuICAgICAgICBpZiAoIXRoaXMuYm9vbVByZWZhYikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcImJvb21QcmVmYWIg6aKE5Yi25L2T5pyq6K6+572uXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgYm9vbU5vZGUgPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmJvb21QcmVmYWIpO1xuICAgICAgICBib29tTm9kZS5uYW1lID0gXCJCb29tXCI7XG5cbiAgICAgICAgY29uc3QgcG9zaXRpb24gPSB0aGlzLmNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uKHgsIHkpO1xuICAgICAgICBib29tTm9kZS5zZXRQb3NpdGlvbihwb3NpdGlvbik7XG5cbiAgICAgICAgdGhpcy5ib2FyZE5vZGUuYWRkQ2hpbGQoYm9vbU5vZGUpO1xuXG4gICAgICAgIC8vIOaSreaUvuWHuueOsOWKqOeUu1xuICAgICAgICBib29tTm9kZS5zZXRTY2FsZSgwKTtcbiAgICAgICAgY2MudHdlZW4oYm9vbU5vZGUpXG4gICAgICAgICAgICAudG8oMC4zLCB7IHNjYWxlWDogMS4yLCBzY2FsZVk6IDEuMiB9LCB7IGVhc2luZzogJ2JhY2tPdXQnIH0pXG4gICAgICAgICAgICAudG8oMC4xLCB7IHNjYWxlWDogMS4wLCBzY2FsZVk6IDEuMCB9KVxuICAgICAgICAgICAgLnN0YXJ0KCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Zyo5oyH5a6a5L2N572u5Yib5bu65qCH6K6w6aKE5Yi25L2TXG4gICAgICovXG4gICAgcHJpdmF0ZSBjcmVhdGVCaWFvamlQcmVmYWIoeDogbnVtYmVyLCB5OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKCF0aGlzLmJpYW9qaVByZWZhYikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcImJpYW9qaVByZWZhYiDpooTliLbkvZPmnKrorr7nva5cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBiaWFvamlOb2RlID0gY2MuaW5zdGFudGlhdGUodGhpcy5iaWFvamlQcmVmYWIpO1xuICAgICAgICBiaWFvamlOb2RlLm5hbWUgPSBcIkJpYW9qaVwiO1xuXG4gICAgICAgIGNvbnN0IHBvc2l0aW9uID0gdGhpcy5jYWxjdWxhdGVQcmVmYWJQb3NpdGlvbih4LCB5KTtcbiAgICAgICAgYmlhb2ppTm9kZS5zZXRQb3NpdGlvbihwb3NpdGlvbik7XG5cbiAgICAgICAgdGhpcy5ib2FyZE5vZGUuYWRkQ2hpbGQoYmlhb2ppTm9kZSk7XG5cbiAgICAgICAgLy8g5pKt5pS+5Ye6546w5Yqo55S7XG4gICAgICAgIGJpYW9qaU5vZGUuc2V0U2NhbGUoMCk7XG4gICAgICAgIGNjLnR3ZWVuKGJpYW9qaU5vZGUpXG4gICAgICAgICAgICAudG8oMC4yLCB7IHNjYWxlWDogMS4wLCBzY2FsZVk6IDEuMCB9LCB7IGVhc2luZzogJ2JhY2tPdXQnIH0pXG4gICAgICAgICAgICAuc3RhcnQoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlnKjmjIflrprkvY3nva7liJvlu7rmlbDlrZfpooTliLbkvZNcbiAgICAgKi9cbiAgICBwcml2YXRlIGNyZWF0ZU51bWJlclByZWZhYih4OiBudW1iZXIsIHk6IG51bWJlciwgbnVtYmVyOiBudW1iZXIpIHtcbiAgICAgICAgaWYgKG51bWJlciA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuOyAvLyAw5LiN6ZyA6KaB5pi+56S6XG4gICAgICAgIH1cblxuICAgICAgICBsZXQgcHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsO1xuICAgICAgICBzd2l0Y2ggKG51bWJlcikge1xuICAgICAgICAgICAgY2FzZSAxOiBwcmVmYWIgPSB0aGlzLmJvb20xUHJlZmFiOyBicmVhaztcbiAgICAgICAgICAgIGNhc2UgMjogcHJlZmFiID0gdGhpcy5ib29tMlByZWZhYjsgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIDM6IHByZWZhYiA9IHRoaXMuYm9vbTNQcmVmYWI7IGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA0OiBwcmVmYWIgPSB0aGlzLmJvb200UHJlZmFiOyBicmVhaztcbiAgICAgICAgICAgIGNhc2UgNTogcHJlZmFiID0gdGhpcy5ib29tNVByZWZhYjsgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIDY6IHByZWZhYiA9IHRoaXMuYm9vbTZQcmVmYWI7IGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA3OiBwcmVmYWIgPSB0aGlzLmJvb203UHJlZmFiOyBicmVhaztcbiAgICAgICAgICAgIGNhc2UgODogcHJlZmFiID0gdGhpcy5ib29tOFByZWZhYjsgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOS4jeaUr+aMgeeahOaVsOWtlzogJHtudW1iZXJ9YCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCFwcmVmYWIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYGJvb20ke251bWJlcn1QcmVmYWIg6aKE5Yi25L2T5pyq6K6+572uYCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBudW1iZXJOb2RlID0gY2MuaW5zdGFudGlhdGUocHJlZmFiKTtcbiAgICAgICAgbnVtYmVyTm9kZS5uYW1lID0gYEJvb20ke251bWJlcn1gO1xuXG4gICAgICAgIGNvbnN0IHBvc2l0aW9uID0gdGhpcy5jYWxjdWxhdGVQcmVmYWJQb3NpdGlvbih4LCB5KTtcbiAgICAgICAgbnVtYmVyTm9kZS5zZXRQb3NpdGlvbihwb3NpdGlvbik7XG5cbiAgICAgICAgdGhpcy5ib2FyZE5vZGUuYWRkQ2hpbGQobnVtYmVyTm9kZSk7XG5cbiAgICAgICAgLy8g5pKt5pS+5Ye6546w5Yqo55S7XG4gICAgICAgIG51bWJlck5vZGUuc2V0U2NhbGUoMCk7XG4gICAgICAgIGNjLnR3ZWVuKG51bWJlck5vZGUpXG4gICAgICAgICAgICAudG8oMC4yLCB7IHNjYWxlWDogMS4wLCBzY2FsZVk6IDEuMCB9LCB7IGVhc2luZzogJ2JhY2tPdXQnIH0pXG4gICAgICAgICAgICAuc3RhcnQoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDnp7vpmaTmjIflrprkvY3nva7nmoTpooTliLbkvZNcbiAgICAgKi9cbiAgICBwcml2YXRlIHJlbW92ZVByZWZhYkF0KHg6IG51bWJlciwgeTogbnVtYmVyKSB7XG4gICAgICAgIC8vIOafpeaJvuW5tuenu+mZpOivpeS9jee9rueahOmihOWItuS9k1xuICAgICAgICBjb25zdCBwb3NpdGlvbiA9IHRoaXMuY2FsY3VsYXRlUHJlZmFiUG9zaXRpb24oeCwgeSk7XG4gICAgICAgIGNvbnN0IHRvbGVyYW5jZSA9IDEwOyAvLyDkvY3nva7lrrnlt65cblxuICAgICAgICB0aGlzLmJvYXJkTm9kZS5jaGlsZHJlbi5mb3JFYWNoKChjaGlsZDogY2MuTm9kZSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgY2hpbGRQb3MgPSBjaGlsZC5nZXRQb3NpdGlvbigpO1xuICAgICAgICAgICAgaWYgKE1hdGguYWJzKGNoaWxkUG9zLnggLSBwb3NpdGlvbi54KSA8IHRvbGVyYW5jZSAmJlxuICAgICAgICAgICAgICAgIE1hdGguYWJzKGNoaWxkUG9zLnkgLSBwb3NpdGlvbi55KSA8IHRvbGVyYW5jZSkge1xuICAgICAgICAgICAgICAgIGlmIChjaGlsZC5uYW1lLmluY2x1ZGVzKFwiQmlhb2ppXCIpIHx8IGNoaWxkLm5hbWUuaW5jbHVkZXMoXCJCb29tXCIpKSB7XG4gICAgICAgICAgICAgICAgICAgIGNoaWxkLnJlbW92ZUZyb21QYXJlbnQoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOa4heepuuaJgOaciemihOWItuS9k1xuICAgICAqL1xuICAgIHB1YmxpYyBjbGVhckFsbFByZWZhYnMoKSB7XG4gICAgICAgIHRoaXMuYm9hcmROb2RlLmNoaWxkcmVuLmZvckVhY2goKGNoaWxkOiBjYy5Ob2RlKSA9PiB7XG4gICAgICAgICAgICBpZiAoY2hpbGQubmFtZS5pbmNsdWRlcyhcIkJpYW9qaVwiKSB8fCBjaGlsZC5uYW1lLmluY2x1ZGVzKFwiQm9vbVwiKSkge1xuICAgICAgICAgICAgICAgIGNoaWxkLnJlbW92ZUZyb21QYXJlbnQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8g6YeN572u5qC85a2Q5pWw5o2uXG4gICAgICAgIGNvbnN0IHsgcm93cywgY29scyB9ID0gdGhpcy5jdXJyZW50TWFwQ29uZmlnLmJvYXJkRGltZW5zaW9ucztcbiAgICAgICAgZm9yIChsZXQgeCA9IDA7IHggPCBjb2xzOyB4KyspIHtcbiAgICAgICAgICAgIGZvciAobGV0IHkgPSAwOyB5IDwgcm93czsgeSsrKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5ncmlkRGF0YVt4XVt5XS5oYXNQbGF5ZXIgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLmlzUmV2ZWFsZWQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLmlzTWFya2VkID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59XG4iXX0=