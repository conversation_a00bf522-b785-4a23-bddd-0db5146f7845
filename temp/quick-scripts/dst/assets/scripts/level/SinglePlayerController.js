
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/SinglePlayerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '882ddIZzIlMnadRsMiIVVUr', 'SinglePlayerController');
// scripts/level/SinglePlayerController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SinglePlayerController = /** @class */ (function (_super) {
    __extends(SinglePlayerController, _super);
    function SinglePlayerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardNode = null; // 棋盘节点
        _this.boomPrefab = null; // 炸弹预制体
        _this.biaojiPrefab = null; // 标记预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 地图配置
        _this.mapConfigs = {
            "8x8": {
                boardSize: { width: 752, height: 845 },
                gridSize: { width: 88, height: 88 },
                boardDimensions: { rows: 8, cols: 8 }
            },
            "9x9": {
                boardSize: { width: 752, height: 747 },
                gridSize: { width: 76, height: 76 },
                boardDimensions: { rows: 9, cols: 9 }
            },
            "9x10": {
                boardSize: { width: 752, height: 830 },
                gridSize: { width: 78, height: 78 },
                boardDimensions: { rows: 9, cols: 10 }
            },
            "10x10": {
                boardSize: { width: 752, height: 745 },
                gridSize: { width: 69, height: 69 },
                boardDimensions: { rows: 10, cols: 10 }
            }
        };
        // 当前地图配置
        _this.currentMapConfig = null;
        _this.currentMapType = "8x8"; // 默认8x8
        // 格子数据存储
        _this.gridData = [];
        _this.gridNodes = [];
        return _this;
    }
    SinglePlayerController.prototype.onLoad = function () {
        // 注册消息监听
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
        // 设置默认地图配置
        this.setMapType(this.currentMapType);
        this.initBoard();
    };
    SinglePlayerController.prototype.onDestroy = function () {
        // 取消消息监听
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 设置地图类型
     * @param mapType 地图类型 "8x8", "9x9", "9x10", "10x10"
     */
    SinglePlayerController.prototype.setMapType = function (mapType) {
        if (this.mapConfigs[mapType]) {
            this.currentMapType = mapType;
            this.currentMapConfig = this.mapConfigs[mapType];
            console.log("\u8BBE\u7F6E\u5730\u56FE\u7C7B\u578B\u4E3A: " + mapType, this.currentMapConfig);
            // 重新初始化棋盘
            if (this.boardNode) {
                this.initBoard();
            }
        }
        else {
            console.error("\u4E0D\u652F\u6301\u7684\u5730\u56FE\u7C7B\u578B: " + mapType);
        }
    };
    /**
     * 设置棋盘节点
     * @param boardNode 棋盘节点
     */
    SinglePlayerController.prototype.setBoardNode = function (boardNode) {
        this.boardNode = boardNode;
        if (this.currentMapConfig) {
            this.initBoard();
        }
    };
    /**
     * 初始化棋盘
     */
    SinglePlayerController.prototype.initBoard = function () {
        if (!this.currentMapConfig || !this.boardNode) {
            console.error("地图配置或棋盘节点未设置");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 初始化格子数据
        this.gridData = [];
        this.gridNodes = [];
        for (var x = 0; x < cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    hasPlayer: false,
                    isRevealed: false,
                    isMarked: false
                };
            }
        }
        this.enableTouchForExistingGrids();
    };
    /**
     * 为现有格子启用触摸事件（模仿联机模式的智能坐标解析）
     */
    SinglePlayerController.prototype.enableTouchForExistingGrids = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        console.log("\u68CB\u76D8\u5B50\u8282\u70B9\u6570\u91CF: " + children.length);
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            console.log("\u5904\u7406\u5B50\u8282\u70B9: " + child.name);
            // 方法1: 尝试从节点名称解析坐标（优先）
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                console.log("\u4ECE\u540D\u79F0\u89E3\u6790\u5750\u6807: (" + coords.x + ", " + coords.y + ")");
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 方法2: 从位置计算坐标（备用）
                var pos = child.getPosition();
                coords = this.getGridCoordinateFromPosition(pos);
                if (coords) {
                    console.log("\u4ECE\u4F4D\u7F6E\u8BA1\u7B97\u5750\u6807: (" + coords.x + ", " + coords.y + ") \u4F4D\u7F6E: (" + pos.x + ", " + pos.y + ")");
                    this.setupGridTouchEvents(child, coords.x, coords.y);
                    this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                    this.gridNodes[coords.x][coords.y] = child;
                }
                else {
                    console.warn("\u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u5750\u6807: " + child.name + ", \u4F4D\u7F6E: (" + pos.x + ", " + pos.y + ")");
                }
            }
        }
    };
    /**
     * 从节点名称解析格子坐标
     */
    SinglePlayerController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配多种格式
        var match;
        // 格式1: Grid_x_y
        match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        // 格式2: block_x_y
        match = nodeName.match(/block_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        // 格式3: 其他可能的格式
        match = nodeName.match(/(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        // 如果节点名称是纯数字，可能表示索引
        if (/^\d+$/.test(nodeName)) {
            var index = parseInt(nodeName);
            var cols = this.currentMapConfig.boardDimensions.cols;
            var x = index % cols;
            var y = Math.floor(index / cols);
            return { x: x, y: y };
        }
        return null;
    };
    /**
     * 从位置计算格子坐标（智能适应不同地图大小）
     */
    SinglePlayerController.prototype.getGridCoordinateFromPosition = function (pos) {
        // 先收集所有格子的位置信息来自动计算参数
        if (!this.boardNode || this.boardNode.children.length === 0) {
            return null;
        }
        var children = this.boardNode.children;
        var positions = [];
        // 收集所有位置
        for (var i = 0; i < children.length; i++) {
            positions.push(children[i].getPosition());
        }
        if (positions.length === 0) {
            return null;
        }
        // 按Y坐标分组，找出行
        var rows = [];
        var tolerance = 5; // 位置容差
        for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {
            var pos_1 = positions_1[_i];
            var foundRow = false;
            for (var _a = 0, rows_1 = rows; _a < rows_1.length; _a++) {
                var row = rows_1[_a];
                if (Math.abs(row[0].y - pos_1.y) < tolerance) {
                    row.push(pos_1);
                    foundRow = true;
                    break;
                }
            }
            if (!foundRow) {
                rows.push([pos_1]);
            }
        }
        // 对每行按X坐标排序
        rows.forEach(function (row) { return row.sort(function (a, b) { return a.x - b.x; }); });
        // 对行按Y坐标排序（从上到下）
        rows.sort(function (a, b) { return b[0].y - a[0].y; });
        // 查找目标位置在哪一行哪一列
        for (var rowIndex = 0; rowIndex < rows.length; rowIndex++) {
            var row = rows[rowIndex];
            for (var colIndex = 0; colIndex < row.length; colIndex++) {
                var gridPos = row[colIndex];
                if (Math.abs(gridPos.x - pos.x) < tolerance && Math.abs(gridPos.y - pos.y) < tolerance) {
                    console.log("\u4F4D\u7F6E\u5339\u914D: pos(" + pos.x.toFixed(2) + ", " + pos.y.toFixed(2) + ") -> \u683C\u5B50(" + colIndex + ", " + rowIndex + ")");
                    return { x: colIndex, y: rowIndex };
                }
            }
        }
        console.warn("\u672A\u627E\u5230\u5339\u914D\u7684\u4F4D\u7F6E: (" + pos.x.toFixed(2) + ", " + pos.y.toFixed(2) + ")");
        return null;
    };
    /**
     * 设置格子触摸事件（替代addTouchEventToGrid）
     */
    SinglePlayerController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (_event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
    };
    /**
     * 格子点击事件 - 发送挖掘操作
     */
    SinglePlayerController.prototype.onGridClick = function (x, y) {
        console.log("\uD83C\uDFAF \u683C\u5B50\u70B9\u51FB: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u274C \u5750\u6807\u65E0\u6548: (" + x + ", " + y + ")");
            return;
        }
        // 检查该位置是否已经被揭开或标记
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed || gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50\u5DF2\u88AB\u64CD\u4F5C: (" + x + ", " + y + "), isRevealed: " + gridData.isRevealed + ", hasPlayer: " + gridData.hasPlayer);
            return;
        }
        console.log("\u2705 \u53D1\u9001\u6316\u6398\u64CD\u4F5C: (" + x + ", " + y + ")");
        // 发送挖掘操作到后端
        this.sendLevelClickBlock(x, y, 1);
    };
    /**
     * 格子长按事件 - 发送标记操作
     */
    SinglePlayerController.prototype.onGridLongPress = function (x, y) {
        console.log("\uD83D\uDD12 \u683C\u5B50\u957F\u6309: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u274C \u5750\u6807\u65E0\u6548: (" + x + ", " + y + ")");
            return;
        }
        // 检查该位置是否已经被揭开
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed) {
            console.warn("\u26A0\uFE0F \u683C\u5B50\u5DF2\u88AB\u63ED\u5F00: (" + x + ", " + y + ")");
            return;
        }
        console.log("\u2705 \u53D1\u9001\u6807\u8BB0\u64CD\u4F5C: (" + x + ", " + y + ")");
        // 发送标记操作到后端
        this.sendLevelClickBlock(x, y, 2);
    };
    /**
     * 发送关卡点击方块消息
     */
    SinglePlayerController.prototype.sendLevelClickBlock = function (x, y, action) {
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        console.log("\u53D1\u9001LevelClickBlock\u6D88\u606F:", clickData);
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    /**
     * 处理后端消息
     */
    SinglePlayerController.prototype.onReceiveMessage = function (data) {
        if (data.msgId === MessageId_1.MessageId.MsgTypeLevelClickBlock) {
            this.handleLevelClickBlockResponse(data.data);
        }
    };
    /**
     * 处理关卡点击方块响应
     */
    SinglePlayerController.prototype.handleLevelClickBlockResponse = function (responseData) {
        var _this = this;
        console.log("收到LevelClickBlock响应:", responseData);
        var x = responseData.x, y = responseData.y, result = responseData.result, action = responseData.action;
        // 更新格子状态
        if (this.isValidCoordinate(x, y)) {
            var gridData = this.gridData[x][y];
            if (action === 1) {
                // 挖掘操作 - 先隐藏格子，再显示结果
                gridData.isRevealed = true;
                gridData.hasPlayer = true;
                // 隐藏格子（模仿联机模式）
                this.removeGridAt(x, y);
                // 延迟显示结果（等格子消失动画完成）
                this.scheduleOnce(function () {
                    if (result === "mine") {
                        // 显示炸弹
                        _this.createBoomPrefab(x, y);
                    }
                    else if (typeof result === "number") {
                        // 显示数字
                        _this.createNumberPrefab(x, y, result);
                    }
                }, 0.3);
            }
            else if (action === 2) {
                // 标记操作
                if (result === "marked") {
                    // 添加标记
                    gridData.isMarked = true;
                    gridData.hasPlayer = true;
                    this.createBiaojiPrefab(x, y);
                }
                else if (result === "unmarked") {
                    // 取消标记
                    gridData.isMarked = false;
                    gridData.hasPlayer = false;
                    this.removePrefabAt(x, y);
                }
            }
        }
    };
    /**
     * 检查坐标是否有效
     */
    SinglePlayerController.prototype.isValidCoordinate = function (x, y) {
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        return x >= 0 && x < cols && y >= 0 && y < rows;
    };
    /**
     * 隐藏指定位置的格子（模仿联机模式的removeGridAt方法）
     */
    SinglePlayerController.prototype.removeGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子（模仿联机模式）
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false; // 隐藏而不是销毁
            })
                .start();
        }
    };
    /**
     * 计算预制体的精确位置（根据不同地图大小使用不同的坐标计算方式）
     */
    SinglePlayerController.prototype.calculatePrefabPosition = function (x, y) {
        // 方法1: 尝试使用格子节点的位置（推荐方式）
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            var gridPos = gridNode.getPosition();
            // 使用格子中心位置，稍微向下偏移一点
            return cc.v2(gridPos.x, gridPos.y - 16);
        }
        // 方法2: 如果没有格子节点，使用数学计算
        var _a = this.currentMapConfig, boardSize = _a.boardSize, gridSize = _a.gridSize;
        // 计算起始位置（左下角）
        var startX = -(boardSize.width / 2) + (gridSize.width / 2);
        var startY = -(boardSize.height / 2) + (gridSize.height / 2);
        // 计算最终位置
        var finalX = startX + (x * gridSize.width);
        var finalY = startY + (y * gridSize.height) - 16; // 向下偏移16像素
        return cc.v2(finalX, finalY);
    };
    /**
     * 在指定位置创建炸弹预制体
     */
    SinglePlayerController.prototype.createBoomPrefab = function (x, y) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置");
            return;
        }
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
    };
    /**
     * 在指定位置创建标记预制体
     */
    SinglePlayerController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置");
            return;
        }
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建数字预制体
     */
    SinglePlayerController.prototype.createNumberPrefab = function (x, y, number) {
        if (number === 0) {
            return; // 0不需要显示
        }
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 移除指定位置的预制体
     */
    SinglePlayerController.prototype.removePrefabAt = function (x, y) {
        // 查找并移除该位置的预制体
        var position = this.calculatePrefabPosition(x, y);
        var tolerance = 10; // 位置容差
        this.boardNode.children.forEach(function (child) {
            var childPos = child.getPosition();
            if (Math.abs(childPos.x - position.x) < tolerance &&
                Math.abs(childPos.y - position.y) < tolerance) {
                if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                    child.removeFromParent();
                }
            }
        });
    };
    /**
     * 清空所有预制体
     */
    SinglePlayerController.prototype.clearAllPrefabs = function () {
        this.boardNode.children.forEach(function (child) {
            if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                child.removeFromParent();
            }
        });
        // 重置格子数据
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        for (var x = 0; x < cols; x++) {
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y].hasPlayer = false;
                this.gridData[x][y].isRevealed = false;
                this.gridData[x][y].isMarked = false;
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], SinglePlayerController.prototype, "boardNode", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom8Prefab", void 0);
    SinglePlayerController = __decorate([
        ccclass
    ], SinglePlayerController);
    return SinglePlayerController;
}(cc.Component));
exports.default = SinglePlayerController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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