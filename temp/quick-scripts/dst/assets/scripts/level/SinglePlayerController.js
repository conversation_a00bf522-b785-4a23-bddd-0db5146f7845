
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/SinglePlayerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '882ddIZzIlMnadRsMiIVVUr', 'SinglePlayerController');
// scripts/level/SinglePlayerController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SinglePlayerController = /** @class */ (function (_super) {
    __extends(SinglePlayerController, _super);
    function SinglePlayerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardNode = null; // 棋盘节点
        _this.boomPrefab = null; // 炸弹预制体
        _this.biaojiPrefab = null; // 标记预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 地图配置
        _this.mapConfigs = {
            "8x8": {
                boardSize: { width: 752, height: 845 },
                gridSize: { width: 88, height: 88 },
                boardDimensions: { rows: 8, cols: 8 }
            },
            "9x9": {
                boardSize: { width: 752, height: 747 },
                gridSize: { width: 76, height: 76 },
                boardDimensions: { rows: 9, cols: 9 }
            },
            "9x10": {
                boardSize: { width: 752, height: 830 },
                gridSize: { width: 78, height: 78 },
                boardDimensions: { rows: 9, cols: 10 }
            },
            "10x10": {
                boardSize: { width: 752, height: 745 },
                gridSize: { width: 69, height: 69 },
                boardDimensions: { rows: 10, cols: 10 }
            }
        };
        // 当前地图配置
        _this.currentMapConfig = null;
        _this.currentMapType = "8x8"; // 默认8x8
        // 格子数据存储
        _this.gridData = [];
        _this.gridNodes = [];
        return _this;
    }
    SinglePlayerController.prototype.onLoad = function () {
        // 注册消息监听
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
        // 设置默认地图配置
        this.setMapType(this.currentMapType);
        this.initBoard();
    };
    SinglePlayerController.prototype.onDestroy = function () {
        // 取消消息监听
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 设置地图类型
     * @param mapType 地图类型 "8x8", "9x9", "9x10", "10x10"
     */
    SinglePlayerController.prototype.setMapType = function (mapType) {
        if (this.mapConfigs[mapType]) {
            this.currentMapType = mapType;
            this.currentMapConfig = this.mapConfigs[mapType];
            console.log("\u8BBE\u7F6E\u5730\u56FE\u7C7B\u578B\u4E3A: " + mapType, this.currentMapConfig);
            // 重新初始化棋盘
            if (this.boardNode) {
                this.initBoard();
            }
        }
        else {
            console.error("\u4E0D\u652F\u6301\u7684\u5730\u56FE\u7C7B\u578B: " + mapType);
        }
    };
    /**
     * 设置棋盘节点
     * @param boardNode 棋盘节点
     */
    SinglePlayerController.prototype.setBoardNode = function (boardNode) {
        this.boardNode = boardNode;
        if (this.currentMapConfig) {
            this.initBoard();
        }
    };
    /**
     * 初始化棋盘
     */
    SinglePlayerController.prototype.initBoard = function () {
        if (!this.currentMapConfig || !this.boardNode) {
            console.error("地图配置或棋盘节点未设置");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 初始化格子数据
        this.gridData = [];
        this.gridNodes = [];
        for (var x = 0; x < cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    hasPlayer: false,
                    isRevealed: false,
                    isMarked: false
                };
            }
        }
        this.enableTouchForExistingGrids();
    };
    /**
     * 为现有格子启用触摸事件
     */
    SinglePlayerController.prototype.enableTouchForExistingGrids = function () {
        var _this = this;
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 遍历棋盘的所有子节点，为格子添加触摸事件
        this.boardNode.children.forEach(function (child, index) {
            // 根据子节点的索引计算格子坐标
            var x = index % cols;
            var y = Math.floor(index / cols);
            if (x < cols && y < rows) {
                _this.addTouchEventToGrid(child, x, y);
                _this.gridNodes[x][y] = child;
            }
        });
    };
    /**
     * 为格子添加触摸事件
     */
    SinglePlayerController.prototype.addTouchEventToGrid = function (gridNode, x, y) {
        var _this = this;
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (_event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
    };
    /**
     * 格子点击事件 - 发送挖掘操作
     */
    SinglePlayerController.prototype.onGridClick = function (x, y) {
        console.log("\u683C\u5B50\u70B9\u51FB: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经被揭开或标记
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed || gridData.hasPlayer) {
            return;
        }
        // 发送挖掘操作到后端
        this.sendLevelClickBlock(x, y, 1);
    };
    /**
     * 格子长按事件 - 发送标记操作
     */
    SinglePlayerController.prototype.onGridLongPress = function (x, y) {
        console.log("\u683C\u5B50\u957F\u6309: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经被揭开
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed) {
            return;
        }
        // 发送标记操作到后端
        this.sendLevelClickBlock(x, y, 2);
    };
    /**
     * 发送关卡点击方块消息
     */
    SinglePlayerController.prototype.sendLevelClickBlock = function (x, y, action) {
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        console.log("\u53D1\u9001LevelClickBlock\u6D88\u606F:", clickData);
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    /**
     * 处理后端消息
     */
    SinglePlayerController.prototype.onReceiveMessage = function (data) {
        if (data.msgId === MessageId_1.MessageId.MsgTypeLevelClickBlock) {
            this.handleLevelClickBlockResponse(data.data);
        }
    };
    /**
     * 处理关卡点击方块响应
     */
    SinglePlayerController.prototype.handleLevelClickBlockResponse = function (responseData) {
        console.log("收到LevelClickBlock响应:", responseData);
        var x = responseData.x, y = responseData.y, result = responseData.result, action = responseData.action;
        // 更新格子状态
        if (this.isValidCoordinate(x, y)) {
            var gridData = this.gridData[x][y];
            if (action === 1) {
                // 挖掘操作
                gridData.isRevealed = true;
                gridData.hasPlayer = true;
                // 根据结果显示相应内容
                if (result === "mine") {
                    // 显示炸弹
                    this.createBoomPrefab(x, y);
                }
                else if (typeof result === "number") {
                    // 显示数字
                    this.createNumberPrefab(x, y, result);
                }
            }
            else if (action === 2) {
                // 标记操作
                if (result === "marked") {
                    // 添加标记
                    gridData.isMarked = true;
                    gridData.hasPlayer = true;
                    this.createBiaojiPrefab(x, y);
                }
                else if (result === "unmarked") {
                    // 取消标记
                    gridData.isMarked = false;
                    gridData.hasPlayer = false;
                    this.removePrefabAt(x, y);
                }
            }
        }
    };
    /**
     * 检查坐标是否有效
     */
    SinglePlayerController.prototype.isValidCoordinate = function (x, y) {
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        return x >= 0 && x < cols && y >= 0 && y < rows;
    };
    /**
     * 计算预制体的精确位置
     */
    SinglePlayerController.prototype.calculatePrefabPosition = function (x, y) {
        var _a = this.currentMapConfig, boardSize = _a.boardSize, gridSize = _a.gridSize;
        // 计算起始位置（左下角）
        var startX = -(boardSize.width / 2) + (gridSize.width / 2);
        var startY = -(boardSize.height / 2) + (gridSize.height / 2);
        // 计算最终位置
        var finalX = startX + (x * gridSize.width);
        var finalY = startY + (y * gridSize.height);
        return cc.v2(finalX, finalY);
    };
    /**
     * 在指定位置创建炸弹预制体
     */
    SinglePlayerController.prototype.createBoomPrefab = function (x, y) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置");
            return;
        }
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
    };
    /**
     * 在指定位置创建标记预制体
     */
    SinglePlayerController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置");
            return;
        }
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建数字预制体
     */
    SinglePlayerController.prototype.createNumberPrefab = function (x, y, number) {
        if (number === 0) {
            return; // 0不需要显示
        }
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 移除指定位置的预制体
     */
    SinglePlayerController.prototype.removePrefabAt = function (x, y) {
        // 查找并移除该位置的预制体
        var position = this.calculatePrefabPosition(x, y);
        var tolerance = 10; // 位置容差
        this.boardNode.children.forEach(function (child) {
            var childPos = child.getPosition();
            if (Math.abs(childPos.x - position.x) < tolerance &&
                Math.abs(childPos.y - position.y) < tolerance) {
                if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                    child.removeFromParent();
                }
            }
        });
    };
    /**
     * 清空所有预制体
     */
    SinglePlayerController.prototype.clearAllPrefabs = function () {
        this.boardNode.children.forEach(function (child) {
            if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                child.removeFromParent();
            }
        });
        // 重置格子数据
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        for (var x = 0; x < cols; x++) {
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y].hasPlayer = false;
                this.gridData[x][y].isRevealed = false;
                this.gridData[x][y].isMarked = false;
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], SinglePlayerController.prototype, "boardNode", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom8Prefab", void 0);
    SinglePlayerController = __decorate([
        ccclass
    ], SinglePlayerController);
    return SinglePlayerController;
}(cc.Component));
exports.default = SinglePlayerController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2xldmVsL1NpbmdsZVBsYXllckNvbnRyb2xsZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsNERBQTJEO0FBQzNELDhDQUE2QztBQUU3QyxxREFBa0Q7QUFDbEQsNkNBQTRDO0FBRXRDLElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBbUI1QztJQUFvRCwwQ0FBWTtJQUFoRTtRQUFBLHFFQTRkQztRQXpkRyxlQUFTLEdBQVksSUFBSSxDQUFDLENBQUMsT0FBTztRQUdsQyxnQkFBVSxHQUFjLElBQUksQ0FBQyxDQUFDLFFBQVE7UUFHdEMsa0JBQVksR0FBYyxJQUFJLENBQUMsQ0FBQyxRQUFRO1FBR3hDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUMsU0FBUztRQUV4QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFFeEMsaUJBQVcsR0FBYyxJQUFJLENBQUMsQ0FBQyxTQUFTO1FBRXhDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUMsU0FBUztRQUV4QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFFeEMsaUJBQVcsR0FBYyxJQUFJLENBQUMsQ0FBQyxTQUFTO1FBRXhDLGlCQUFXLEdBQWMsSUFBSSxDQUFDLENBQUMsU0FBUztRQUV4QyxpQkFBVyxHQUFjLElBQUksQ0FBQyxDQUFDLFNBQVM7UUFFeEMsT0FBTztRQUNDLGdCQUFVLEdBQWlDO1lBQy9DLEtBQUssRUFBRTtnQkFDSCxTQUFTLEVBQUUsRUFBRSxLQUFLLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUU7Z0JBQ3RDLFFBQVEsRUFBRSxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLEVBQUUsRUFBRTtnQkFDbkMsZUFBZSxFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFO2FBQ3hDO1lBQ0QsS0FBSyxFQUFFO2dCQUNILFNBQVMsRUFBRSxFQUFFLEtBQUssRUFBRSxHQUFHLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRTtnQkFDdEMsUUFBUSxFQUFFLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUUsRUFBRSxFQUFFO2dCQUNuQyxlQUFlLEVBQUUsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUU7YUFDeEM7WUFDRCxNQUFNLEVBQUU7Z0JBQ0osU0FBUyxFQUFFLEVBQUUsS0FBSyxFQUFFLEdBQUcsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFO2dCQUN0QyxRQUFRLEVBQUUsRUFBRSxLQUFLLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxFQUFFLEVBQUU7Z0JBQ25DLGVBQWUsRUFBRSxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRTthQUN6QztZQUNELE9BQU8sRUFBRTtnQkFDTCxTQUFTLEVBQUUsRUFBRSxLQUFLLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUU7Z0JBQ3RDLFFBQVEsRUFBRSxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLEVBQUUsRUFBRTtnQkFDbkMsZUFBZSxFQUFFLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxJQUFJLEVBQUUsRUFBRSxFQUFFO2FBQzFDO1NBQ0osQ0FBQztRQUVGLFNBQVM7UUFDRCxzQkFBZ0IsR0FBYyxJQUFJLENBQUM7UUFDbkMsb0JBQWMsR0FBVyxLQUFLLENBQUMsQ0FBQyxRQUFRO1FBRWhELFNBQVM7UUFDRCxjQUFRLEdBQWlCLEVBQUUsQ0FBQztRQUM1QixlQUFTLEdBQWdCLEVBQUUsQ0FBQzs7SUFrYXhDLENBQUM7SUFoYUcsdUNBQU0sR0FBTjtRQUNJLFNBQVM7UUFDVCxpQkFBTyxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyx1QkFBUyxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFdEYsV0FBVztRQUNYLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBQ3JDLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQztJQUNyQixDQUFDO0lBRUQsMENBQVMsR0FBVDtRQUNJLFNBQVM7UUFDVCxpQkFBTyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyx1QkFBUyxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLENBQUM7SUFDN0YsQ0FBQztJQUVEOzs7T0FHRztJQUNJLDJDQUFVLEdBQWpCLFVBQWtCLE9BQWU7UUFDN0IsSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxFQUFFO1lBQzFCLElBQUksQ0FBQyxjQUFjLEdBQUcsT0FBTyxDQUFDO1lBQzlCLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQ2pELE9BQU8sQ0FBQyxHQUFHLENBQUMsaURBQVksT0FBUyxFQUFFLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1lBRTFELFVBQVU7WUFDVixJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7Z0JBQ2hCLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQzthQUNwQjtTQUNKO2FBQU07WUFDSCxPQUFPLENBQUMsS0FBSyxDQUFDLHVEQUFhLE9BQVMsQ0FBQyxDQUFDO1NBQ3pDO0lBQ0wsQ0FBQztJQUVEOzs7T0FHRztJQUNJLDZDQUFZLEdBQW5CLFVBQW9CLFNBQWtCO1FBQ2xDLElBQUksQ0FBQyxTQUFTLEdBQUcsU0FBUyxDQUFDO1FBQzNCLElBQUksSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQ3ZCLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQztTQUNwQjtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLDBDQUFTLEdBQWpCO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUU7WUFDM0MsT0FBTyxDQUFDLEtBQUssQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUM5QixPQUFPO1NBQ1Y7UUFFSyxJQUFBLEtBQWlCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxlQUFlLEVBQXBELElBQUksVUFBQSxFQUFFLElBQUksVUFBMEMsQ0FBQztRQUU3RCxVQUFVO1FBQ1YsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDbkIsSUFBSSxDQUFDLFNBQVMsR0FBRyxFQUFFLENBQUM7UUFFcEIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUMzQixJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztZQUN0QixJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztZQUN2QixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFO2dCQUMzQixJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHO29CQUNsQixDQUFDLEVBQUUsQ0FBQztvQkFDSixDQUFDLEVBQUUsQ0FBQztvQkFDSixTQUFTLEVBQUUsS0FBSztvQkFDaEIsVUFBVSxFQUFFLEtBQUs7b0JBQ2pCLFFBQVEsRUFBRSxLQUFLO2lCQUNsQixDQUFDO2FBQ0w7U0FDSjtRQUVELElBQUksQ0FBQywyQkFBMkIsRUFBRSxDQUFDO0lBQ3ZDLENBQUM7SUFFRDs7T0FFRztJQUNLLDREQUEyQixHQUFuQztRQUFBLGlCQW1CQztRQWxCRyxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsRUFBRTtZQUNqQixPQUFPLENBQUMsS0FBSyxDQUFDLG1CQUFtQixDQUFDLENBQUM7WUFDbkMsT0FBTztTQUNWO1FBRUssSUFBQSxLQUFpQixJQUFJLENBQUMsZ0JBQWdCLENBQUMsZUFBZSxFQUFwRCxJQUFJLFVBQUEsRUFBRSxJQUFJLFVBQTBDLENBQUM7UUFFN0QsdUJBQXVCO1FBQ3ZCLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxVQUFDLEtBQWMsRUFBRSxLQUFhO1lBQzFELGlCQUFpQjtZQUNqQixJQUFNLENBQUMsR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDO1lBQ3ZCLElBQU0sQ0FBQyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxDQUFDO1lBRW5DLElBQUksQ0FBQyxHQUFHLElBQUksSUFBSSxDQUFDLEdBQUcsSUFBSSxFQUFFO2dCQUN0QixLQUFJLENBQUMsbUJBQW1CLENBQUMsS0FBSyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztnQkFDdEMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUM7YUFDaEM7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNQLENBQUM7SUFFRDs7T0FFRztJQUNLLG9EQUFtQixHQUEzQixVQUE0QixRQUFpQixFQUFFLENBQVMsRUFBRSxDQUFTO1FBQW5FLGlCQWdEQztRQS9DRyxTQUFTO1FBQ1QsSUFBSSxjQUFjLEdBQUcsS0FBSyxDQUFDO1FBQzNCLElBQUksY0FBYyxHQUFHLENBQUMsQ0FBQztRQUN2QixJQUFJLGlCQUFpQixHQUFhLElBQUksQ0FBQztRQUN2QyxJQUFNLGVBQWUsR0FBRyxHQUFHLENBQUMsQ0FBQyxTQUFTO1FBRXRDLFNBQVM7UUFDVCxRQUFRLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsRUFBRSxVQUFDLE1BQTJCO1lBQ25FLGNBQWMsR0FBRyxJQUFJLENBQUM7WUFDdEIsY0FBYyxHQUFHLENBQUMsQ0FBQztZQUVuQixTQUFTO1lBQ1QsaUJBQWlCLEdBQUc7Z0JBQ2hCLElBQUksY0FBYyxFQUFFO29CQUNoQixjQUFjLElBQUksR0FBRyxDQUFDO29CQUN0QixJQUFJLGNBQWMsSUFBSSxlQUFlLEVBQUU7d0JBQ25DLEtBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO3dCQUMzQixjQUFjLEdBQUcsS0FBSyxDQUFDO3dCQUN2QixJQUFJLGlCQUFpQixFQUFFOzRCQUNuQixLQUFJLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLENBQUM7eUJBQ3RDO3FCQUNKO2lCQUNKO1lBQ0wsQ0FBQyxDQUFDO1lBQ0YsS0FBSSxDQUFDLFFBQVEsQ0FBQyxpQkFBaUIsRUFBRSxHQUFHLENBQUMsQ0FBQztRQUMxQyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFVCxTQUFTO1FBQ1QsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLEVBQUUsVUFBQyxNQUEyQjtZQUNqRSxpQkFBaUI7WUFDakIsSUFBSSxjQUFjLElBQUksY0FBYyxHQUFHLGVBQWUsRUFBRTtnQkFDcEQsS0FBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7YUFDMUI7WUFFRCxjQUFjLEdBQUcsS0FBSyxDQUFDO1lBQ3ZCLElBQUksaUJBQWlCLEVBQUU7Z0JBQ25CLEtBQUksQ0FBQyxVQUFVLENBQUMsaUJBQWlCLENBQUMsQ0FBQzthQUN0QztRQUNMLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUVULFNBQVM7UUFDVCxRQUFRLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksRUFBRSxVQUFDLE1BQTJCO1lBQ3BFLGNBQWMsR0FBRyxLQUFLLENBQUM7WUFDdkIsSUFBSSxpQkFBaUIsRUFBRTtnQkFDbkIsS0FBSSxDQUFDLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO2FBQ3RDO1FBQ0wsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDO0lBQ2IsQ0FBQztJQUVEOztPQUVHO0lBQ0ssNENBQVcsR0FBbkIsVUFBb0IsQ0FBUyxFQUFFLENBQVM7UUFDcEMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQ0FBVSxDQUFDLFVBQUssQ0FBQyxNQUFHLENBQUMsQ0FBQztRQUVsQyxXQUFXO1FBQ1gsSUFBSSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7WUFDL0IsT0FBTztTQUNWO1FBRUQsa0JBQWtCO1FBQ2xCLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDckMsSUFBSSxRQUFRLENBQUMsVUFBVSxJQUFJLFFBQVEsQ0FBQyxTQUFTLEVBQUU7WUFDM0MsT0FBTztTQUNWO1FBRUQsWUFBWTtRQUNaLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ3RDLENBQUM7SUFFRDs7T0FFRztJQUNLLGdEQUFlLEdBQXZCLFVBQXdCLENBQVMsRUFBRSxDQUFTO1FBQ3hDLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0NBQVUsQ0FBQyxVQUFLLENBQUMsTUFBRyxDQUFDLENBQUM7UUFFbEMsV0FBVztRQUNYLElBQUksQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO1lBQy9CLE9BQU87U0FDVjtRQUVELGVBQWU7UUFDZixJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ3JDLElBQUksUUFBUSxDQUFDLFVBQVUsRUFBRTtZQUNyQixPQUFPO1NBQ1Y7UUFFRCxZQUFZO1FBQ1osSUFBSSxDQUFDLG1CQUFtQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDdEMsQ0FBQztJQUVEOztPQUVHO0lBQ0ssb0RBQW1CLEdBQTNCLFVBQTRCLENBQVMsRUFBRSxDQUFTLEVBQUUsTUFBYztRQUM1RCxJQUFNLFNBQVMsR0FBc0I7WUFDakMsQ0FBQyxFQUFFLENBQUM7WUFDSixDQUFDLEVBQUUsQ0FBQztZQUNKLE1BQU0sRUFBRSxNQUFNLENBQUMscUJBQXFCO1NBQ3ZDLENBQUM7UUFFRixPQUFPLENBQUMsR0FBRyxDQUFDLDBDQUFzQixFQUFFLFNBQVMsQ0FBQyxDQUFDO1FBQy9DLG1DQUFnQixDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxxQkFBUyxDQUFDLHNCQUFzQixFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBQ3hGLENBQUM7SUFFRDs7T0FFRztJQUNLLGlEQUFnQixHQUF4QixVQUF5QixJQUFTO1FBQzlCLElBQUksSUFBSSxDQUFDLEtBQUssS0FBSyxxQkFBUyxDQUFDLHNCQUFzQixFQUFFO1lBQ2pELElBQUksQ0FBQyw2QkFBNkIsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7U0FDakQ7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyw4REFBNkIsR0FBckMsVUFBc0MsWUFBcUM7UUFDdkUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQkFBc0IsRUFBRSxZQUFZLENBQUMsQ0FBQztRQUUxQyxJQUFBLENBQUMsR0FBd0IsWUFBWSxFQUFwQyxFQUFFLENBQUMsR0FBcUIsWUFBWSxFQUFqQyxFQUFFLE1BQU0sR0FBYSxZQUFZLE9BQXpCLEVBQUUsTUFBTSxHQUFLLFlBQVksT0FBakIsQ0FBa0I7UUFFOUMsU0FBUztRQUNULElBQUksSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtZQUM5QixJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBRXJDLElBQUksTUFBTSxLQUFLLENBQUMsRUFBRTtnQkFDZCxPQUFPO2dCQUNQLFFBQVEsQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO2dCQUMzQixRQUFRLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQztnQkFFMUIsYUFBYTtnQkFDYixJQUFJLE1BQU0sS0FBSyxNQUFNLEVBQUU7b0JBQ25CLE9BQU87b0JBQ1AsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztpQkFDL0I7cUJBQU0sSUFBSSxPQUFPLE1BQU0sS0FBSyxRQUFRLEVBQUU7b0JBQ25DLE9BQU87b0JBQ1AsSUFBSSxDQUFDLGtCQUFrQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsTUFBTSxDQUFDLENBQUM7aUJBQ3pDO2FBQ0o7aUJBQU0sSUFBSSxNQUFNLEtBQUssQ0FBQyxFQUFFO2dCQUNyQixPQUFPO2dCQUNQLElBQUksTUFBTSxLQUFLLFFBQVEsRUFBRTtvQkFDckIsT0FBTztvQkFDUCxRQUFRLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQztvQkFDekIsUUFBUSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUM7b0JBQzFCLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7aUJBQ2pDO3FCQUFNLElBQUksTUFBTSxLQUFLLFVBQVUsRUFBRTtvQkFDOUIsT0FBTztvQkFDUCxRQUFRLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQztvQkFDMUIsUUFBUSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUM7b0JBQzNCLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO2lCQUM3QjthQUNKO1NBQ0o7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxrREFBaUIsR0FBekIsVUFBMEIsQ0FBUyxFQUFFLENBQVM7UUFDcEMsSUFBQSxLQUFpQixJQUFJLENBQUMsZ0JBQWdCLENBQUMsZUFBZSxFQUFwRCxJQUFJLFVBQUEsRUFBRSxJQUFJLFVBQTBDLENBQUM7UUFDN0QsT0FBTyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDO0lBQ3BELENBQUM7SUFFRDs7T0FFRztJQUNLLHdEQUF1QixHQUEvQixVQUFnQyxDQUFTLEVBQUUsQ0FBUztRQUMxQyxJQUFBLEtBQTBCLElBQUksQ0FBQyxnQkFBZ0IsRUFBN0MsU0FBUyxlQUFBLEVBQUUsUUFBUSxjQUEwQixDQUFDO1FBRXRELGNBQWM7UUFDZCxJQUFNLE1BQU0sR0FBRyxDQUFDLENBQUMsU0FBUyxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFDN0QsSUFBTSxNQUFNLEdBQUcsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBRS9ELFNBQVM7UUFDVCxJQUFNLE1BQU0sR0FBRyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQzdDLElBQU0sTUFBTSxHQUFHLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUM7UUFFOUMsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBRUQ7O09BRUc7SUFDSyxpREFBZ0IsR0FBeEIsVUFBeUIsQ0FBUyxFQUFFLENBQVM7UUFDekMsSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUU7WUFDbEIsT0FBTyxDQUFDLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1lBQ25DLE9BQU87U0FDVjtRQUVELElBQU0sUUFBUSxHQUFHLEVBQUUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDO1FBQ2pELFFBQVEsQ0FBQyxJQUFJLEdBQUcsTUFBTSxDQUFDO1FBRXZCLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDcEQsUUFBUSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUUvQixJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUVsQyxTQUFTO1FBQ1QsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNyQixFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQzthQUNiLEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQUUsQ0FBQzthQUM1RCxFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLENBQUM7YUFDckMsS0FBSyxFQUFFLENBQUM7SUFDakIsQ0FBQztJQUVEOztPQUVHO0lBQ0ssbURBQWtCLEdBQTFCLFVBQTJCLENBQVMsRUFBRSxDQUFTO1FBQzNDLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ3BCLE9BQU8sQ0FBQyxLQUFLLENBQUMscUJBQXFCLENBQUMsQ0FBQztZQUNyQyxPQUFPO1NBQ1Y7UUFFRCxJQUFNLFVBQVUsR0FBRyxFQUFFLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUNyRCxVQUFVLENBQUMsSUFBSSxHQUFHLFFBQVEsQ0FBQztRQUUzQixJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsdUJBQXVCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBQ3BELFVBQVUsQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLENBQUM7UUFFakMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDLENBQUM7UUFFcEMsU0FBUztRQUNULFVBQVUsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDdkIsRUFBRSxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUM7YUFDZixFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUUsU0FBUyxFQUFFLENBQUM7YUFDNUQsS0FBSyxFQUFFLENBQUM7SUFDakIsQ0FBQztJQUVEOztPQUVHO0lBQ0ssbURBQWtCLEdBQTFCLFVBQTJCLENBQVMsRUFBRSxDQUFTLEVBQUUsTUFBYztRQUMzRCxJQUFJLE1BQU0sS0FBSyxDQUFDLEVBQUU7WUFDZCxPQUFPLENBQUMsU0FBUztTQUNwQjtRQUVELElBQUksTUFBTSxHQUFjLElBQUksQ0FBQztRQUM3QixRQUFRLE1BQU0sRUFBRTtZQUNaLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDLEtBQUssQ0FBQztnQkFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFBQyxNQUFNO1lBQ3pDO2dCQUNJLE9BQU8sQ0FBQyxLQUFLLENBQUMsMkNBQVcsTUFBUSxDQUFDLENBQUM7Z0JBQ25DLE9BQU87U0FDZDtRQUVELElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDVCxPQUFPLENBQUMsS0FBSyxDQUFDLFNBQU8sTUFBTSxnREFBZSxDQUFDLENBQUM7WUFDNUMsT0FBTztTQUNWO1FBRUQsSUFBTSxVQUFVLEdBQUcsRUFBRSxDQUFDLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUMxQyxVQUFVLENBQUMsSUFBSSxHQUFHLFNBQU8sTUFBUSxDQUFDO1FBRWxDLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDcEQsVUFBVSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUVqQyxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQztRQUVwQyxTQUFTO1FBQ1QsVUFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN2QixFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQzthQUNmLEVBQUUsQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQUUsQ0FBQzthQUM1RCxLQUFLLEVBQUUsQ0FBQztJQUNqQixDQUFDO0lBRUQ7O09BRUc7SUFDSywrQ0FBYyxHQUF0QixVQUF1QixDQUFTLEVBQUUsQ0FBUztRQUN2QyxlQUFlO1FBQ2YsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNwRCxJQUFNLFNBQVMsR0FBRyxFQUFFLENBQUMsQ0FBQyxPQUFPO1FBRTdCLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxVQUFDLEtBQWM7WUFDM0MsSUFBTSxRQUFRLEdBQUcsS0FBSyxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ3JDLElBQUksSUFBSSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxHQUFHLFFBQVEsQ0FBQyxDQUFDLENBQUMsR0FBRyxTQUFTO2dCQUM3QyxJQUFJLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDLEdBQUcsUUFBUSxDQUFDLENBQUMsQ0FBQyxHQUFHLFNBQVMsRUFBRTtnQkFDL0MsSUFBSSxLQUFLLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsSUFBSSxLQUFLLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsRUFBRTtvQkFDOUQsS0FBSyxDQUFDLGdCQUFnQixFQUFFLENBQUM7aUJBQzVCO2FBQ0o7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNQLENBQUM7SUFFRDs7T0FFRztJQUNJLGdEQUFlLEdBQXRCO1FBQ0ksSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUMsS0FBYztZQUMzQyxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxFQUFFO2dCQUM5RCxLQUFLLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQzthQUM1QjtRQUNMLENBQUMsQ0FBQyxDQUFDO1FBRUgsU0FBUztRQUNILElBQUEsS0FBaUIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLGVBQWUsRUFBcEQsSUFBSSxVQUFBLEVBQUUsSUFBSSxVQUEwQyxDQUFDO1FBQzdELEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDM0IsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRTtnQkFDM0IsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLEdBQUcsS0FBSyxDQUFDO2dCQUN0QyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7Z0JBQ3ZDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQzthQUN4QztTQUNKO0lBQ0wsQ0FBQztJQXhkRDtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzZEQUNRO0lBRzFCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7OERBQ1M7SUFHN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQztnRUFDVztJQUcvQjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOytEQUNVO0lBRTlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7K0RBQ1U7SUFFOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzsrREFDVTtJQUU5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOytEQUNVO0lBRTlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7K0RBQ1U7SUFFOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzsrREFDVTtJQUU5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOytEQUNVO0lBRTlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7K0RBQ1U7SUExQmIsc0JBQXNCO1FBRDFDLE9BQU87T0FDYSxzQkFBc0IsQ0E0ZDFDO0lBQUQsNkJBQUM7Q0E1ZEQsQUE0ZEMsQ0E1ZG1ELEVBQUUsQ0FBQyxTQUFTLEdBNGQvRDtrQkE1ZG9CLHNCQUFzQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFdlYlNvY2tldE1hbmFnZXIgfSBmcm9tICcuLi9uZXQvV2ViU29ja2V0TWFuYWdlcic7XG5pbXBvcnQgeyBNZXNzYWdlSWQgfSBmcm9tICcuLi9uZXQvTWVzc2FnZUlkJztcbmltcG9ydCB7IENsaWNrQmxvY2tSZXF1ZXN0LCBMZXZlbENsaWNrQmxvY2tSZXNwb25zZSB9IGZyb20gJy4uL2JlYW4vR2FtZUJlYW4nO1xuaW1wb3J0IHsgRXZlbnRUeXBlIH0gZnJvbSAnLi4vY29tbW9uL0V2ZW50Q2VudGVyJztcbmltcG9ydCB7IEdhbWVNZ3IgfSBmcm9tICcuLi9jb21tb24vR2FtZU1ncic7XG5cbmNvbnN0IHsgY2NjbGFzcywgcHJvcGVydHkgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbi8vIOWcsOWbvumFjee9ruaOpeWPo1xuaW50ZXJmYWNlIE1hcENvbmZpZyB7XG4gICAgYm9hcmRTaXplOiB7IHdpZHRoOiBudW1iZXIsIGhlaWdodDogbnVtYmVyIH07ICAvLyDmo4vnm5jlpKflsI9cbiAgICBncmlkU2l6ZTogeyB3aWR0aDogbnVtYmVyLCBoZWlnaHQ6IG51bWJlciB9OyAgIC8vIOagvOWtkOWkp+Wwj1xuICAgIGJvYXJkRGltZW5zaW9uczogeyByb3dzOiBudW1iZXIsIGNvbHM6IG51bWJlciB9OyAvLyDooYzliJfmlbBcbn1cblxuLy8g5qC85a2Q5pWw5o2u5o6l5Y+jXG5pbnRlcmZhY2UgR3JpZERhdGEge1xuICAgIHg6IG51bWJlcjtcbiAgICB5OiBudW1iZXI7XG4gICAgaGFzUGxheWVyOiBib29sZWFuO1xuICAgIGlzUmV2ZWFsZWQ6IGJvb2xlYW47XG4gICAgaXNNYXJrZWQ6IGJvb2xlYW47XG59XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBTaW5nbGVQbGF5ZXJDb250cm9sbGVyIGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGJvYXJkTm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIOaji+ebmOiKgueCuVxuXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tUHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAvLyDngrjlvLnpooTliLbkvZNcblxuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYmlhb2ppUHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAvLyDmoIforrDpooTliLbkvZNcblxuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbTFQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOaVsOWtlzHpooTliLbkvZNcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb20yUHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAvLyDmlbDlrZcy6aKE5Yi25L2TXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tM1ByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgLy8g5pWw5a2XM+mihOWItuS9k1xuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbTRQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOaVsOWtlzTpooTliLbkvZNcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb201UHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAvLyDmlbDlrZc16aKE5Yi25L2TXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBib29tNlByZWZhYjogY2MuUHJlZmFiID0gbnVsbDsgLy8g5pWw5a2XNumihOWItuS9k1xuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgYm9vbTdQcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7IC8vIOaVsOWtlzfpooTliLbkvZNcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGJvb204UHJlZmFiOiBjYy5QcmVmYWIgPSBudWxsOyAvLyDmlbDlrZc46aKE5Yi25L2TXG5cbiAgICAvLyDlnLDlm77phY3nva5cbiAgICBwcml2YXRlIG1hcENvbmZpZ3M6IHsgW2tleTogc3RyaW5nXTogTWFwQ29uZmlnIH0gPSB7XG4gICAgICAgIFwiOHg4XCI6IHtcbiAgICAgICAgICAgIGJvYXJkU2l6ZTogeyB3aWR0aDogNzUyLCBoZWlnaHQ6IDg0NSB9LFxuICAgICAgICAgICAgZ3JpZFNpemU6IHsgd2lkdGg6IDg4LCBoZWlnaHQ6IDg4IH0sXG4gICAgICAgICAgICBib2FyZERpbWVuc2lvbnM6IHsgcm93czogOCwgY29sczogOCB9XG4gICAgICAgIH0sXG4gICAgICAgIFwiOXg5XCI6IHtcbiAgICAgICAgICAgIGJvYXJkU2l6ZTogeyB3aWR0aDogNzUyLCBoZWlnaHQ6IDc0NyB9LFxuICAgICAgICAgICAgZ3JpZFNpemU6IHsgd2lkdGg6IDc2LCBoZWlnaHQ6IDc2IH0sXG4gICAgICAgICAgICBib2FyZERpbWVuc2lvbnM6IHsgcm93czogOSwgY29sczogOSB9XG4gICAgICAgIH0sXG4gICAgICAgIFwiOXgxMFwiOiB7XG4gICAgICAgICAgICBib2FyZFNpemU6IHsgd2lkdGg6IDc1MiwgaGVpZ2h0OiA4MzAgfSxcbiAgICAgICAgICAgIGdyaWRTaXplOiB7IHdpZHRoOiA3OCwgaGVpZ2h0OiA3OCB9LFxuICAgICAgICAgICAgYm9hcmREaW1lbnNpb25zOiB7IHJvd3M6IDksIGNvbHM6IDEwIH1cbiAgICAgICAgfSxcbiAgICAgICAgXCIxMHgxMFwiOiB7XG4gICAgICAgICAgICBib2FyZFNpemU6IHsgd2lkdGg6IDc1MiwgaGVpZ2h0OiA3NDUgfSxcbiAgICAgICAgICAgIGdyaWRTaXplOiB7IHdpZHRoOiA2OSwgaGVpZ2h0OiA2OSB9LFxuICAgICAgICAgICAgYm9hcmREaW1lbnNpb25zOiB7IHJvd3M6IDEwLCBjb2xzOiAxMCB9XG4gICAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8g5b2T5YmN5Zyw5Zu+6YWN572uXG4gICAgcHJpdmF0ZSBjdXJyZW50TWFwQ29uZmlnOiBNYXBDb25maWcgPSBudWxsO1xuICAgIHByaXZhdGUgY3VycmVudE1hcFR5cGU6IHN0cmluZyA9IFwiOHg4XCI7IC8vIOm7mOiupDh4OFxuXG4gICAgLy8g5qC85a2Q5pWw5o2u5a2Y5YKoXG4gICAgcHJpdmF0ZSBncmlkRGF0YTogR3JpZERhdGFbXVtdID0gW107XG4gICAgcHJpdmF0ZSBncmlkTm9kZXM6IGNjLk5vZGVbXVtdID0gW107XG5cbiAgICBvbkxvYWQoKSB7XG4gICAgICAgIC8vIOazqOWGjOa2iOaBr+ebkeWQrFxuICAgICAgICBHYW1lTWdyLkV2ZW50LkFkZEV2ZW50TGlzdGVuZXIoRXZlbnRUeXBlLlJlY2VpdmVNZXNzYWdlLCB0aGlzLm9uUmVjZWl2ZU1lc3NhZ2UsIHRoaXMpO1xuXG4gICAgICAgIC8vIOiuvue9rum7mOiupOWcsOWbvumFjee9rlxuICAgICAgICB0aGlzLnNldE1hcFR5cGUodGhpcy5jdXJyZW50TWFwVHlwZSk7XG4gICAgICAgIHRoaXMuaW5pdEJvYXJkKCk7XG4gICAgfVxuXG4gICAgb25EZXN0cm95KCkge1xuICAgICAgICAvLyDlj5bmtojmtojmga/nm5HlkKxcbiAgICAgICAgR2FtZU1nci5FdmVudC5SZW1vdmVFdmVudExpc3RlbmVyKEV2ZW50VHlwZS5SZWNlaXZlTWVzc2FnZSwgdGhpcy5vblJlY2VpdmVNZXNzYWdlLCB0aGlzKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDorr7nva7lnLDlm77nsbvlnotcbiAgICAgKiBAcGFyYW0gbWFwVHlwZSDlnLDlm77nsbvlnosgXCI4eDhcIiwgXCI5eDlcIiwgXCI5eDEwXCIsIFwiMTB4MTBcIlxuICAgICAqL1xuICAgIHB1YmxpYyBzZXRNYXBUeXBlKG1hcFR5cGU6IHN0cmluZykge1xuICAgICAgICBpZiAodGhpcy5tYXBDb25maWdzW21hcFR5cGVdKSB7XG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRNYXBUeXBlID0gbWFwVHlwZTtcbiAgICAgICAgICAgIHRoaXMuY3VycmVudE1hcENvbmZpZyA9IHRoaXMubWFwQ29uZmlnc1ttYXBUeXBlXTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDorr7nva7lnLDlm77nsbvlnovkuLo6ICR7bWFwVHlwZX1gLCB0aGlzLmN1cnJlbnRNYXBDb25maWcpO1xuXG4gICAgICAgICAgICAvLyDph43mlrDliJ3lp4vljJbmo4vnm5hcbiAgICAgICAgICAgIGlmICh0aGlzLmJvYXJkTm9kZSkge1xuICAgICAgICAgICAgICAgIHRoaXMuaW5pdEJvYXJkKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGDkuI3mlK/mjIHnmoTlnLDlm77nsbvlnos6ICR7bWFwVHlwZX1gKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiuvue9ruaji+ebmOiKgueCuVxuICAgICAqIEBwYXJhbSBib2FyZE5vZGUg5qOL55uY6IqC54K5XG4gICAgICovXG4gICAgcHVibGljIHNldEJvYXJkTm9kZShib2FyZE5vZGU6IGNjLk5vZGUpIHtcbiAgICAgICAgdGhpcy5ib2FyZE5vZGUgPSBib2FyZE5vZGU7XG4gICAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXBDb25maWcpIHtcbiAgICAgICAgICAgIHRoaXMuaW5pdEJvYXJkKCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDliJ3lp4vljJbmo4vnm5hcbiAgICAgKi9cbiAgICBwcml2YXRlIGluaXRCb2FyZCgpIHtcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnRNYXBDb25maWcgfHwgIXRoaXMuYm9hcmROb2RlKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi5Zyw5Zu+6YWN572u5oiW5qOL55uY6IqC54K55pyq6K6+572uXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgeyByb3dzLCBjb2xzIH0gPSB0aGlzLmN1cnJlbnRNYXBDb25maWcuYm9hcmREaW1lbnNpb25zO1xuICAgICAgICBcbiAgICAgICAgLy8g5Yid5aeL5YyW5qC85a2Q5pWw5o2uXG4gICAgICAgIHRoaXMuZ3JpZERhdGEgPSBbXTtcbiAgICAgICAgdGhpcy5ncmlkTm9kZXMgPSBbXTtcbiAgICAgICAgXG4gICAgICAgIGZvciAobGV0IHggPSAwOyB4IDwgY29sczsgeCsrKSB7XG4gICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdID0gW107XG4gICAgICAgICAgICB0aGlzLmdyaWROb2Rlc1t4XSA9IFtdO1xuICAgICAgICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCByb3dzOyB5KyspIHtcbiAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldID0ge1xuICAgICAgICAgICAgICAgICAgICB4OiB4LFxuICAgICAgICAgICAgICAgICAgICB5OiB5LFxuICAgICAgICAgICAgICAgICAgICBoYXNQbGF5ZXI6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBpc1JldmVhbGVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgaXNNYXJrZWQ6IGZhbHNlXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIHRoaXMuZW5hYmxlVG91Y2hGb3JFeGlzdGluZ0dyaWRzKCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Li6546w5pyJ5qC85a2Q5ZCv55So6Kem5pG45LqL5Lu2XG4gICAgICovXG4gICAgcHJpdmF0ZSBlbmFibGVUb3VjaEZvckV4aXN0aW5nR3JpZHMoKSB7XG4gICAgICAgIGlmICghdGhpcy5ib2FyZE5vZGUpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCLmo4vnm5joioLngrnmnKrorr7nva7vvIzml6Dms5XlkK/nlKjop6bmkbjkuovku7bvvIFcIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCB7IHJvd3MsIGNvbHMgfSA9IHRoaXMuY3VycmVudE1hcENvbmZpZy5ib2FyZERpbWVuc2lvbnM7XG5cbiAgICAgICAgLy8g6YGN5Y6G5qOL55uY55qE5omA5pyJ5a2Q6IqC54K577yM5Li65qC85a2Q5re75Yqg6Kem5pG45LqL5Lu2XG4gICAgICAgIHRoaXMuYm9hcmROb2RlLmNoaWxkcmVuLmZvckVhY2goKGNoaWxkOiBjYy5Ob2RlLCBpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgICAgICAgICAvLyDmoLnmja7lrZDoioLngrnnmoTntKLlvJXorqHnrpfmoLzlrZDlnZDmoIdcbiAgICAgICAgICAgIGNvbnN0IHggPSBpbmRleCAlIGNvbHM7XG4gICAgICAgICAgICBjb25zdCB5ID0gTWF0aC5mbG9vcihpbmRleCAvIGNvbHMpO1xuXG4gICAgICAgICAgICBpZiAoeCA8IGNvbHMgJiYgeSA8IHJvd3MpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmFkZFRvdWNoRXZlbnRUb0dyaWQoY2hpbGQsIHgsIHkpO1xuICAgICAgICAgICAgICAgIHRoaXMuZ3JpZE5vZGVzW3hdW3ldID0gY2hpbGQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOS4uuagvOWtkOa3u+WKoOinpuaRuOS6i+S7tlxuICAgICAqL1xuICAgIHByaXZhdGUgYWRkVG91Y2hFdmVudFRvR3JpZChncmlkTm9kZTogY2MuTm9kZSwgeDogbnVtYmVyLCB5OiBudW1iZXIpIHtcbiAgICAgICAgLy8g6ZW/5oyJ55u45YWz5Y+Y6YePXG4gICAgICAgIGxldCBpc0xvbmdQcmVzc2luZyA9IGZhbHNlO1xuICAgICAgICBsZXQgbG9uZ1ByZXNzVGltZXIgPSAwO1xuICAgICAgICBsZXQgbG9uZ1ByZXNzQ2FsbGJhY2s6IEZ1bmN0aW9uID0gbnVsbDtcbiAgICAgICAgY29uc3QgTE9OR19QUkVTU19USU1FID0gMS4wOyAvLyAx56eS6ZW/5oyJ5pe26Ze0XG5cbiAgICAgICAgLy8g6Kem5pG45byA5aeL5LqL5Lu2XG4gICAgICAgIGdyaWROb2RlLm9uKGNjLk5vZGUuRXZlbnRUeXBlLlRPVUNIX1NUQVJULCAoX2V2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoKSA9PiB7XG4gICAgICAgICAgICBpc0xvbmdQcmVzc2luZyA9IHRydWU7XG4gICAgICAgICAgICBsb25nUHJlc3NUaW1lciA9IDA7XG5cbiAgICAgICAgICAgIC8vIOW8gOWni+mVv+aMieajgOa1i1xuICAgICAgICAgICAgbG9uZ1ByZXNzQ2FsbGJhY2sgPSAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGlzTG9uZ1ByZXNzaW5nKSB7XG4gICAgICAgICAgICAgICAgICAgIGxvbmdQcmVzc1RpbWVyICs9IDAuMTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGxvbmdQcmVzc1RpbWVyID49IExPTkdfUFJFU1NfVElNRSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5vbkdyaWRMb25nUHJlc3MoeCwgeSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpc0xvbmdQcmVzc2luZyA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGxvbmdQcmVzc0NhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy51bnNjaGVkdWxlKGxvbmdQcmVzc0NhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICB0aGlzLnNjaGVkdWxlKGxvbmdQcmVzc0NhbGxiYWNrLCAwLjEpO1xuICAgICAgICB9LCB0aGlzKTtcblxuICAgICAgICAvLyDop6bmkbjnu5PmnZ/kuovku7ZcbiAgICAgICAgZ3JpZE5vZGUub24oY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfRU5ELCAoX2V2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoKSA9PiB7XG4gICAgICAgICAgICAvLyDlpoLmnpzkuI3mmK/plb/mjInvvIzliJnmiafooYzngrnlh7vkuovku7ZcbiAgICAgICAgICAgIGlmIChpc0xvbmdQcmVzc2luZyAmJiBsb25nUHJlc3NUaW1lciA8IExPTkdfUFJFU1NfVElNRSkge1xuICAgICAgICAgICAgICAgIHRoaXMub25HcmlkQ2xpY2soeCwgeSk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlzTG9uZ1ByZXNzaW5nID0gZmFsc2U7XG4gICAgICAgICAgICBpZiAobG9uZ1ByZXNzQ2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICB0aGlzLnVuc2NoZWR1bGUobG9uZ1ByZXNzQ2FsbGJhY2spO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCB0aGlzKTtcblxuICAgICAgICAvLyDop6bmkbjlj5bmtojkuovku7ZcbiAgICAgICAgZ3JpZE5vZGUub24oY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfQ0FOQ0VMLCAoX2V2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoKSA9PiB7XG4gICAgICAgICAgICBpc0xvbmdQcmVzc2luZyA9IGZhbHNlO1xuICAgICAgICAgICAgaWYgKGxvbmdQcmVzc0NhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgdGhpcy51bnNjaGVkdWxlKGxvbmdQcmVzc0NhbGxiYWNrKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSwgdGhpcyk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5qC85a2Q54K55Ye75LqL5Lu2IC0g5Y+R6YCB5oyW5o6Y5pON5L2cXG4gICAgICovXG4gICAgcHJpdmF0ZSBvbkdyaWRDbGljayh4OiBudW1iZXIsIHk6IG51bWJlcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg5qC85a2Q54K55Ye7OiAoJHt4fSwgJHt5fSlgKTtcblxuICAgICAgICAvLyDmo4Dmn6XlnZDmoIfmmK/lkKbmnInmlYhcbiAgICAgICAgaWYgKCF0aGlzLmlzVmFsaWRDb29yZGluYXRlKHgsIHkpKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmo4Dmn6Xor6XkvY3nva7mmK/lkKblt7Lnu4/ooqvmj63lvIDmiJbmoIforrBcbiAgICAgICAgY29uc3QgZ3JpZERhdGEgPSB0aGlzLmdyaWREYXRhW3hdW3ldO1xuICAgICAgICBpZiAoZ3JpZERhdGEuaXNSZXZlYWxlZCB8fCBncmlkRGF0YS5oYXNQbGF5ZXIpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWPkemAgeaMluaOmOaTjeS9nOWIsOWQjuerr1xuICAgICAgICB0aGlzLnNlbmRMZXZlbENsaWNrQmxvY2soeCwgeSwgMSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5qC85a2Q6ZW/5oyJ5LqL5Lu2IC0g5Y+R6YCB5qCH6K6w5pON5L2cXG4gICAgICovXG4gICAgcHJpdmF0ZSBvbkdyaWRMb25nUHJlc3MoeDogbnVtYmVyLCB5OiBudW1iZXIpIHtcbiAgICAgICAgY29uc29sZS5sb2coYOagvOWtkOmVv+aMiTogKCR7eH0sICR7eX0pYCk7XG5cbiAgICAgICAgLy8g5qOA5p+l5Z2Q5qCH5piv5ZCm5pyJ5pWIXG4gICAgICAgIGlmICghdGhpcy5pc1ZhbGlkQ29vcmRpbmF0ZSh4LCB5KSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5qOA5p+l6K+l5L2N572u5piv5ZCm5bey57uP6KKr5o+t5byAXG4gICAgICAgIGNvbnN0IGdyaWREYXRhID0gdGhpcy5ncmlkRGF0YVt4XVt5XTtcbiAgICAgICAgaWYgKGdyaWREYXRhLmlzUmV2ZWFsZWQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWPkemAgeagh+iusOaTjeS9nOWIsOWQjuerr1xuICAgICAgICB0aGlzLnNlbmRMZXZlbENsaWNrQmxvY2soeCwgeSwgMik7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Y+R6YCB5YWz5Y2h54K55Ye75pa55Z2X5raI5oGvXG4gICAgICovXG4gICAgcHJpdmF0ZSBzZW5kTGV2ZWxDbGlja0Jsb2NrKHg6IG51bWJlciwgeTogbnVtYmVyLCBhY3Rpb246IG51bWJlcikge1xuICAgICAgICBjb25zdCBjbGlja0RhdGE6IENsaWNrQmxvY2tSZXF1ZXN0ID0ge1xuICAgICAgICAgICAgeDogeCxcbiAgICAgICAgICAgIHk6IHksXG4gICAgICAgICAgICBhY3Rpb246IGFjdGlvbiAvLyAxPeaMluaOmOaWueWdl++8jDI95qCH6K6wL+WPlua2iOagh+iusOWcsOmbt1xuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnNvbGUubG9nKGDlj5HpgIFMZXZlbENsaWNrQmxvY2vmtojmga86YCwgY2xpY2tEYXRhKTtcbiAgICAgICAgV2ViU29ja2V0TWFuYWdlci5HZXRJbnN0YW5jZSgpLnNlbmRNc2coTWVzc2FnZUlkLk1zZ1R5cGVMZXZlbENsaWNrQmxvY2ssIGNsaWNrRGF0YSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CG5ZCO56uv5raI5oGvXG4gICAgICovXG4gICAgcHJpdmF0ZSBvblJlY2VpdmVNZXNzYWdlKGRhdGE6IGFueSkge1xuICAgICAgICBpZiAoZGF0YS5tc2dJZCA9PT0gTWVzc2FnZUlkLk1zZ1R5cGVMZXZlbENsaWNrQmxvY2spIHtcbiAgICAgICAgICAgIHRoaXMuaGFuZGxlTGV2ZWxDbGlja0Jsb2NrUmVzcG9uc2UoZGF0YS5kYXRhKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhuWFs+WNoeeCueWHu+aWueWdl+WTjeW6lFxuICAgICAqL1xuICAgIHByaXZhdGUgaGFuZGxlTGV2ZWxDbGlja0Jsb2NrUmVzcG9uc2UocmVzcG9uc2VEYXRhOiBMZXZlbENsaWNrQmxvY2tSZXNwb25zZSkge1xuICAgICAgICBjb25zb2xlLmxvZyhcIuaUtuWIsExldmVsQ2xpY2tCbG9ja+WTjeW6lDpcIiwgcmVzcG9uc2VEYXRhKTtcblxuICAgICAgICBjb25zdCB7IHgsIHksIHJlc3VsdCwgYWN0aW9uIH0gPSByZXNwb25zZURhdGE7XG4gICAgICAgIFxuICAgICAgICAvLyDmm7TmlrDmoLzlrZDnirbmgIFcbiAgICAgICAgaWYgKHRoaXMuaXNWYWxpZENvb3JkaW5hdGUoeCwgeSkpIHtcbiAgICAgICAgICAgIGNvbnN0IGdyaWREYXRhID0gdGhpcy5ncmlkRGF0YVt4XVt5XTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgaWYgKGFjdGlvbiA9PT0gMSkge1xuICAgICAgICAgICAgICAgIC8vIOaMluaOmOaTjeS9nFxuICAgICAgICAgICAgICAgIGdyaWREYXRhLmlzUmV2ZWFsZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgIGdyaWREYXRhLmhhc1BsYXllciA9IHRydWU7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgLy8g5qC55o2u57uT5p6c5pi+56S655u45bqU5YaF5a65XG4gICAgICAgICAgICAgICAgaWYgKHJlc3VsdCA9PT0gXCJtaW5lXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8g5pi+56S654K45by5XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlQm9vbVByZWZhYih4LCB5KTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiByZXN1bHQgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8g5pi+56S65pWw5a2XXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlTnVtYmVyUHJlZmFiKHgsIHksIHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmIChhY3Rpb24gPT09IDIpIHtcbiAgICAgICAgICAgICAgICAvLyDmoIforrDmk43kvZxcbiAgICAgICAgICAgICAgICBpZiAocmVzdWx0ID09PSBcIm1hcmtlZFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOa3u+WKoOagh+iusFxuICAgICAgICAgICAgICAgICAgICBncmlkRGF0YS5pc01hcmtlZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIGdyaWREYXRhLmhhc1BsYXllciA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlQmlhb2ppUHJlZmFiKHgsIHkpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzdWx0ID09PSBcInVubWFya2VkXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8g5Y+W5raI5qCH6K6wXG4gICAgICAgICAgICAgICAgICAgIGdyaWREYXRhLmlzTWFya2VkID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIGdyaWREYXRhLmhhc1BsYXllciA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbW92ZVByZWZhYkF0KHgsIHkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOajgOafpeWdkOagh+aYr+WQpuacieaViFxuICAgICAqL1xuICAgIHByaXZhdGUgaXNWYWxpZENvb3JkaW5hdGUoeDogbnVtYmVyLCB5OiBudW1iZXIpOiBib29sZWFuIHtcbiAgICAgICAgY29uc3QgeyByb3dzLCBjb2xzIH0gPSB0aGlzLmN1cnJlbnRNYXBDb25maWcuYm9hcmREaW1lbnNpb25zO1xuICAgICAgICByZXR1cm4geCA+PSAwICYmIHggPCBjb2xzICYmIHkgPj0gMCAmJiB5IDwgcm93cztcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDorqHnrpfpooTliLbkvZPnmoTnsr7noa7kvY3nva5cbiAgICAgKi9cbiAgICBwcml2YXRlIGNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uKHg6IG51bWJlciwgeTogbnVtYmVyKTogY2MuVmVjMiB7XG4gICAgICAgIGNvbnN0IHsgYm9hcmRTaXplLCBncmlkU2l6ZSB9ID0gdGhpcy5jdXJyZW50TWFwQ29uZmlnO1xuXG4gICAgICAgIC8vIOiuoeeul+i1t+Wni+S9jee9ru+8iOW3puS4i+inku+8iVxuICAgICAgICBjb25zdCBzdGFydFggPSAtKGJvYXJkU2l6ZS53aWR0aCAvIDIpICsgKGdyaWRTaXplLndpZHRoIC8gMik7XG4gICAgICAgIGNvbnN0IHN0YXJ0WSA9IC0oYm9hcmRTaXplLmhlaWdodCAvIDIpICsgKGdyaWRTaXplLmhlaWdodCAvIDIpO1xuXG4gICAgICAgIC8vIOiuoeeul+acgOe7iOS9jee9rlxuICAgICAgICBjb25zdCBmaW5hbFggPSBzdGFydFggKyAoeCAqIGdyaWRTaXplLndpZHRoKTtcbiAgICAgICAgY29uc3QgZmluYWxZID0gc3RhcnRZICsgKHkgKiBncmlkU2l6ZS5oZWlnaHQpO1xuXG4gICAgICAgIHJldHVybiBjYy52MihmaW5hbFgsIGZpbmFsWSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5Zyo5oyH5a6a5L2N572u5Yib5bu654K45by56aKE5Yi25L2TXG4gICAgICovXG4gICAgcHJpdmF0ZSBjcmVhdGVCb29tUHJlZmFiKHg6IG51bWJlciwgeTogbnVtYmVyKSB7XG4gICAgICAgIGlmICghdGhpcy5ib29tUHJlZmFiKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiYm9vbVByZWZhYiDpooTliLbkvZPmnKrorr7nva5cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBib29tTm9kZSA9IGNjLmluc3RhbnRpYXRlKHRoaXMuYm9vbVByZWZhYik7XG4gICAgICAgIGJvb21Ob2RlLm5hbWUgPSBcIkJvb21cIjtcblxuICAgICAgICBjb25zdCBwb3NpdGlvbiA9IHRoaXMuY2FsY3VsYXRlUHJlZmFiUG9zaXRpb24oeCwgeSk7XG4gICAgICAgIGJvb21Ob2RlLnNldFBvc2l0aW9uKHBvc2l0aW9uKTtcblxuICAgICAgICB0aGlzLmJvYXJkTm9kZS5hZGRDaGlsZChib29tTm9kZSk7XG5cbiAgICAgICAgLy8g5pKt5pS+5Ye6546w5Yqo55S7XG4gICAgICAgIGJvb21Ob2RlLnNldFNjYWxlKDApO1xuICAgICAgICBjYy50d2Vlbihib29tTm9kZSlcbiAgICAgICAgICAgIC50bygwLjMsIHsgc2NhbGVYOiAxLjIsIHNjYWxlWTogMS4yIH0sIHsgZWFzaW5nOiAnYmFja091dCcgfSlcbiAgICAgICAgICAgIC50bygwLjEsIHsgc2NhbGVYOiAxLjAsIHNjYWxlWTogMS4wIH0pXG4gICAgICAgICAgICAuc3RhcnQoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlnKjmjIflrprkvY3nva7liJvlu7rmoIforrDpooTliLbkvZNcbiAgICAgKi9cbiAgICBwcml2YXRlIGNyZWF0ZUJpYW9qaVByZWZhYih4OiBudW1iZXIsIHk6IG51bWJlcikge1xuICAgICAgICBpZiAoIXRoaXMuYmlhb2ppUHJlZmFiKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiYmlhb2ppUHJlZmFiIOmihOWItuS9k+acquiuvue9rlwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGJpYW9qaU5vZGUgPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmJpYW9qaVByZWZhYik7XG4gICAgICAgIGJpYW9qaU5vZGUubmFtZSA9IFwiQmlhb2ppXCI7XG5cbiAgICAgICAgY29uc3QgcG9zaXRpb24gPSB0aGlzLmNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uKHgsIHkpO1xuICAgICAgICBiaWFvamlOb2RlLnNldFBvc2l0aW9uKHBvc2l0aW9uKTtcblxuICAgICAgICB0aGlzLmJvYXJkTm9kZS5hZGRDaGlsZChiaWFvamlOb2RlKTtcblxuICAgICAgICAvLyDmkq3mlL7lh7rnjrDliqjnlLtcbiAgICAgICAgYmlhb2ppTm9kZS5zZXRTY2FsZSgwKTtcbiAgICAgICAgY2MudHdlZW4oYmlhb2ppTm9kZSlcbiAgICAgICAgICAgIC50bygwLjIsIHsgc2NhbGVYOiAxLjAsIHNjYWxlWTogMS4wIH0sIHsgZWFzaW5nOiAnYmFja091dCcgfSlcbiAgICAgICAgICAgIC5zdGFydCgpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWcqOaMh+WumuS9jee9ruWIm+W7uuaVsOWtl+mihOWItuS9k1xuICAgICAqL1xuICAgIHByaXZhdGUgY3JlYXRlTnVtYmVyUHJlZmFiKHg6IG51bWJlciwgeTogbnVtYmVyLCBudW1iZXI6IG51bWJlcikge1xuICAgICAgICBpZiAobnVtYmVyID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm47IC8vIDDkuI3pnIDopoHmmL7npLpcbiAgICAgICAgfVxuXG4gICAgICAgIGxldCBwcmVmYWI6IGNjLlByZWZhYiA9IG51bGw7XG4gICAgICAgIHN3aXRjaCAobnVtYmVyKSB7XG4gICAgICAgICAgICBjYXNlIDE6IHByZWZhYiA9IHRoaXMuYm9vbTFQcmVmYWI7IGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAyOiBwcmVmYWIgPSB0aGlzLmJvb20yUHJlZmFiOyBicmVhaztcbiAgICAgICAgICAgIGNhc2UgMzogcHJlZmFiID0gdGhpcy5ib29tM1ByZWZhYjsgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIDQ6IHByZWZhYiA9IHRoaXMuYm9vbTRQcmVmYWI7IGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA1OiBwcmVmYWIgPSB0aGlzLmJvb201UHJlZmFiOyBicmVhaztcbiAgICAgICAgICAgIGNhc2UgNjogcHJlZmFiID0gdGhpcy5ib29tNlByZWZhYjsgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIDc6IHByZWZhYiA9IHRoaXMuYm9vbTdQcmVmYWI7IGJyZWFrO1xuICAgICAgICAgICAgY2FzZSA4OiBwcmVmYWIgPSB0aGlzLmJvb204UHJlZmFiOyBicmVhaztcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihg5LiN5pSv5oyB55qE5pWw5a2XOiAke251bWJlcn1gKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXByZWZhYikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihgYm9vbSR7bnVtYmVyfVByZWZhYiDpooTliLbkvZPmnKrorr7nva5gKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IG51bWJlck5vZGUgPSBjYy5pbnN0YW50aWF0ZShwcmVmYWIpO1xuICAgICAgICBudW1iZXJOb2RlLm5hbWUgPSBgQm9vbSR7bnVtYmVyfWA7XG5cbiAgICAgICAgY29uc3QgcG9zaXRpb24gPSB0aGlzLmNhbGN1bGF0ZVByZWZhYlBvc2l0aW9uKHgsIHkpO1xuICAgICAgICBudW1iZXJOb2RlLnNldFBvc2l0aW9uKHBvc2l0aW9uKTtcblxuICAgICAgICB0aGlzLmJvYXJkTm9kZS5hZGRDaGlsZChudW1iZXJOb2RlKTtcblxuICAgICAgICAvLyDmkq3mlL7lh7rnjrDliqjnlLtcbiAgICAgICAgbnVtYmVyTm9kZS5zZXRTY2FsZSgwKTtcbiAgICAgICAgY2MudHdlZW4obnVtYmVyTm9kZSlcbiAgICAgICAgICAgIC50bygwLjIsIHsgc2NhbGVYOiAxLjAsIHNjYWxlWTogMS4wIH0sIHsgZWFzaW5nOiAnYmFja091dCcgfSlcbiAgICAgICAgICAgIC5zdGFydCgpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOenu+mZpOaMh+WumuS9jee9rueahOmihOWItuS9k1xuICAgICAqL1xuICAgIHByaXZhdGUgcmVtb3ZlUHJlZmFiQXQoeDogbnVtYmVyLCB5OiBudW1iZXIpIHtcbiAgICAgICAgLy8g5p+l5om+5bm256e76Zmk6K+l5L2N572u55qE6aKE5Yi25L2TXG4gICAgICAgIGNvbnN0IHBvc2l0aW9uID0gdGhpcy5jYWxjdWxhdGVQcmVmYWJQb3NpdGlvbih4LCB5KTtcbiAgICAgICAgY29uc3QgdG9sZXJhbmNlID0gMTA7IC8vIOS9jee9ruWuueW3rlxuXG4gICAgICAgIHRoaXMuYm9hcmROb2RlLmNoaWxkcmVuLmZvckVhY2goKGNoaWxkOiBjYy5Ob2RlKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBjaGlsZFBvcyA9IGNoaWxkLmdldFBvc2l0aW9uKCk7XG4gICAgICAgICAgICBpZiAoTWF0aC5hYnMoY2hpbGRQb3MueCAtIHBvc2l0aW9uLngpIDwgdG9sZXJhbmNlICYmXG4gICAgICAgICAgICAgICAgTWF0aC5hYnMoY2hpbGRQb3MueSAtIHBvc2l0aW9uLnkpIDwgdG9sZXJhbmNlKSB7XG4gICAgICAgICAgICAgICAgaWYgKGNoaWxkLm5hbWUuaW5jbHVkZXMoXCJCaWFvamlcIikgfHwgY2hpbGQubmFtZS5pbmNsdWRlcyhcIkJvb21cIikpIHtcbiAgICAgICAgICAgICAgICAgICAgY2hpbGQucmVtb3ZlRnJvbVBhcmVudCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5riF56m65omA5pyJ6aKE5Yi25L2TXG4gICAgICovXG4gICAgcHVibGljIGNsZWFyQWxsUHJlZmFicygpIHtcbiAgICAgICAgdGhpcy5ib2FyZE5vZGUuY2hpbGRyZW4uZm9yRWFjaCgoY2hpbGQ6IGNjLk5vZGUpID0+IHtcbiAgICAgICAgICAgIGlmIChjaGlsZC5uYW1lLmluY2x1ZGVzKFwiQmlhb2ppXCIpIHx8IGNoaWxkLm5hbWUuaW5jbHVkZXMoXCJCb29tXCIpKSB7XG4gICAgICAgICAgICAgICAgY2hpbGQucmVtb3ZlRnJvbVBhcmVudCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcblxuICAgICAgICAvLyDph43nva7moLzlrZDmlbDmja5cbiAgICAgICAgY29uc3QgeyByb3dzLCBjb2xzIH0gPSB0aGlzLmN1cnJlbnRNYXBDb25maWcuYm9hcmREaW1lbnNpb25zO1xuICAgICAgICBmb3IgKGxldCB4ID0gMDsgeCA8IGNvbHM7IHgrKykge1xuICAgICAgICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCByb3dzOyB5KyspIHtcbiAgICAgICAgICAgICAgICB0aGlzLmdyaWREYXRhW3hdW3ldLmhhc1BsYXllciA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIHRoaXMuZ3JpZERhdGFbeF1beV0uaXNSZXZlYWxlZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIHRoaXMuZ3JpZERhdGFbeF1beV0uaXNNYXJrZWQgPSBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbn1cbiJdfQ==