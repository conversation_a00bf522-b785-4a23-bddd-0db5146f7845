
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/SinglePlayerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '882ddIZzIlMnadRsMiIVVUr', 'SinglePlayerController');
// scripts/level/SinglePlayerController.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SinglePlayerController = /** @class */ (function (_super) {
    __extends(SinglePlayerController, _super);
    function SinglePlayerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardNode = null; // 棋盘节点
        _this.boomPrefab = null; // 炸弹预制体
        _this.biaojiPrefab = null; // 标记预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 地图配置
        _this.mapConfigs = {
            "8x8": {
                boardSize: { width: 752, height: 845 },
                gridSize: { width: 88, height: 88 },
                boardDimensions: { rows: 8, cols: 8 }
            },
            "9x9": {
                boardSize: { width: 752, height: 747 },
                gridSize: { width: 76, height: 76 },
                boardDimensions: { rows: 9, cols: 9 }
            },
            "9x10": {
                boardSize: { width: 752, height: 830 },
                gridSize: { width: 78, height: 78 },
                boardDimensions: { rows: 9, cols: 10 }
            },
            "10x10": {
                boardSize: { width: 752, height: 745 },
                gridSize: { width: 69, height: 69 },
                boardDimensions: { rows: 10, cols: 10 }
            }
        };
        // 当前地图配置
        _this.currentMapConfig = null;
        _this.currentMapType = "8x8"; // 默认8x8
        // 格子数据存储
        _this.gridData = [];
        _this.gridNodes = [];
        return _this;
    }
    SinglePlayerController.prototype.onLoad = function () {
        // 注册消息监听
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
        // 设置默认地图配置
        this.setMapType(this.currentMapType);
        this.initBoard();
    };
    SinglePlayerController.prototype.onDestroy = function () {
        // 取消消息监听
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 设置地图类型
     * @param mapType 地图类型 "8x8", "9x9", "9x10", "10x10"
     */
    SinglePlayerController.prototype.setMapType = function (mapType) {
        if (this.mapConfigs[mapType]) {
            this.currentMapType = mapType;
            this.currentMapConfig = this.mapConfigs[mapType];
            console.log("\u8BBE\u7F6E\u5730\u56FE\u7C7B\u578B\u4E3A: " + mapType, this.currentMapConfig);
            // 重新初始化棋盘
            if (this.boardNode) {
                this.initBoard();
            }
        }
        else {
            console.error("\u4E0D\u652F\u6301\u7684\u5730\u56FE\u7C7B\u578B: " + mapType);
        }
    };
    /**
     * 设置棋盘节点
     * @param boardNode 棋盘节点
     */
    SinglePlayerController.prototype.setBoardNode = function (boardNode) {
        this.boardNode = boardNode;
        if (this.currentMapConfig) {
            this.initBoard();
        }
    };
    /**
     * 初始化棋盘
     */
    SinglePlayerController.prototype.initBoard = function () {
        if (!this.currentMapConfig || !this.boardNode) {
            console.error("地图配置或棋盘节点未设置");
            return;
        }
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        // 初始化格子数据
        this.gridData = [];
        this.gridNodes = [];
        for (var x = 0; x < cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    hasPlayer: false,
                    isRevealed: false,
                    isMarked: false
                };
            }
        }
        this.enableTouchForExistingGrids();
    };
    /**
     * 为现有格子启用触摸事件（模仿联机模式的智能坐标解析）
     */
    SinglePlayerController.prototype.enableTouchForExistingGrids = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        console.log("\u68CB\u76D8\u5B50\u8282\u70B9\u6570\u91CF: " + children.length);
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            console.log("\u5904\u7406\u5B50\u8282\u70B9: " + child.name);
            // 方法1: 尝试从节点名称解析坐标（优先）
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                console.log("\u4ECE\u540D\u79F0\u89E3\u6790\u5750\u6807: (" + coords.x + ", " + coords.y + ")");
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 方法2: 从位置计算坐标（备用）
                var pos = child.getPosition();
                coords = this.getGridCoordinateFromPosition(pos);
                if (coords) {
                    console.log("\u4ECE\u4F4D\u7F6E\u8BA1\u7B97\u5750\u6807: (" + coords.x + ", " + coords.y + ") \u4F4D\u7F6E: (" + pos.x + ", " + pos.y + ")");
                    this.setupGridTouchEvents(child, coords.x, coords.y);
                    this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                    this.gridNodes[coords.x][coords.y] = child;
                }
                else {
                    console.warn("\u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u5750\u6807: " + child.name + ", \u4F4D\u7F6E: (" + pos.x + ", " + pos.y + ")");
                }
            }
        }
    };
    /**
     * 从节点名称解析格子坐标
     */
    SinglePlayerController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    /**
     * 从位置计算格子坐标
     */
    SinglePlayerController.prototype.getGridCoordinateFromPosition = function (pos) {
        var _a = this.currentMapConfig, boardSize = _a.boardSize, gridSize = _a.gridSize;
        // 计算格子坐标
        var x = Math.floor((pos.x + boardSize.width / 2) / gridSize.width);
        var y = Math.floor((pos.y + boardSize.height / 2) / gridSize.height);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    /**
     * 设置格子触摸事件（替代addTouchEventToGrid）
     */
    SinglePlayerController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (_event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
    };
    /**
     * 格子点击事件 - 发送挖掘操作
     */
    SinglePlayerController.prototype.onGridClick = function (x, y) {
        console.log("\uD83C\uDFAF \u683C\u5B50\u70B9\u51FB: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u274C \u5750\u6807\u65E0\u6548: (" + x + ", " + y + ")");
            return;
        }
        // 检查该位置是否已经被揭开或标记
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed || gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50\u5DF2\u88AB\u64CD\u4F5C: (" + x + ", " + y + "), isRevealed: " + gridData.isRevealed + ", hasPlayer: " + gridData.hasPlayer);
            return;
        }
        console.log("\u2705 \u53D1\u9001\u6316\u6398\u64CD\u4F5C: (" + x + ", " + y + ")");
        // 发送挖掘操作到后端
        this.sendLevelClickBlock(x, y, 1);
    };
    /**
     * 格子长按事件 - 发送标记操作
     */
    SinglePlayerController.prototype.onGridLongPress = function (x, y) {
        console.log("\u683C\u5B50\u957F\u6309: (" + x + ", " + y + ")");
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经被揭开
        var gridData = this.gridData[x][y];
        if (gridData.isRevealed) {
            return;
        }
        // 发送标记操作到后端
        this.sendLevelClickBlock(x, y, 2);
    };
    /**
     * 发送关卡点击方块消息
     */
    SinglePlayerController.prototype.sendLevelClickBlock = function (x, y, action) {
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        console.log("\u53D1\u9001LevelClickBlock\u6D88\u606F:", clickData);
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    /**
     * 处理后端消息
     */
    SinglePlayerController.prototype.onReceiveMessage = function (data) {
        if (data.msgId === MessageId_1.MessageId.MsgTypeLevelClickBlock) {
            this.handleLevelClickBlockResponse(data.data);
        }
    };
    /**
     * 处理关卡点击方块响应
     */
    SinglePlayerController.prototype.handleLevelClickBlockResponse = function (responseData) {
        var _this = this;
        console.log("收到LevelClickBlock响应:", responseData);
        var x = responseData.x, y = responseData.y, result = responseData.result, action = responseData.action;
        // 更新格子状态
        if (this.isValidCoordinate(x, y)) {
            var gridData = this.gridData[x][y];
            if (action === 1) {
                // 挖掘操作 - 先隐藏格子，再显示结果
                gridData.isRevealed = true;
                gridData.hasPlayer = true;
                // 隐藏格子（模仿联机模式）
                this.removeGridAt(x, y);
                // 延迟显示结果（等格子消失动画完成）
                this.scheduleOnce(function () {
                    if (result === "mine") {
                        // 显示炸弹
                        _this.createBoomPrefab(x, y);
                    }
                    else if (typeof result === "number") {
                        // 显示数字
                        _this.createNumberPrefab(x, y, result);
                    }
                }, 0.3);
            }
            else if (action === 2) {
                // 标记操作
                if (result === "marked") {
                    // 添加标记
                    gridData.isMarked = true;
                    gridData.hasPlayer = true;
                    this.createBiaojiPrefab(x, y);
                }
                else if (result === "unmarked") {
                    // 取消标记
                    gridData.isMarked = false;
                    gridData.hasPlayer = false;
                    this.removePrefabAt(x, y);
                }
            }
        }
    };
    /**
     * 检查坐标是否有效
     */
    SinglePlayerController.prototype.isValidCoordinate = function (x, y) {
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        return x >= 0 && x < cols && y >= 0 && y < rows;
    };
    /**
     * 隐藏指定位置的格子（模仿联机模式的removeGridAt方法）
     */
    SinglePlayerController.prototype.removeGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子（模仿联机模式）
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false; // 隐藏而不是销毁
            })
                .start();
        }
    };
    /**
     * 计算预制体的精确位置（根据不同地图大小使用不同的坐标计算方式）
     */
    SinglePlayerController.prototype.calculatePrefabPosition = function (x, y) {
        // 方法1: 尝试使用格子节点的位置（推荐方式）
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            var gridPos = gridNode.getPosition();
            // 使用格子中心位置，稍微向下偏移一点
            return cc.v2(gridPos.x, gridPos.y - 16);
        }
        // 方法2: 如果没有格子节点，使用数学计算
        var _a = this.currentMapConfig, boardSize = _a.boardSize, gridSize = _a.gridSize;
        // 计算起始位置（左下角）
        var startX = -(boardSize.width / 2) + (gridSize.width / 2);
        var startY = -(boardSize.height / 2) + (gridSize.height / 2);
        // 计算最终位置
        var finalX = startX + (x * gridSize.width);
        var finalY = startY + (y * gridSize.height) - 16; // 向下偏移16像素
        return cc.v2(finalX, finalY);
    };
    /**
     * 在指定位置创建炸弹预制体
     */
    SinglePlayerController.prototype.createBoomPrefab = function (x, y) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置");
            return;
        }
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
    };
    /**
     * 在指定位置创建标记预制体
     */
    SinglePlayerController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置");
            return;
        }
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建数字预制体
     */
    SinglePlayerController.prototype.createNumberPrefab = function (x, y, number) {
        if (number === 0) {
            return; // 0不需要显示
        }
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 移除指定位置的预制体
     */
    SinglePlayerController.prototype.removePrefabAt = function (x, y) {
        // 查找并移除该位置的预制体
        var position = this.calculatePrefabPosition(x, y);
        var tolerance = 10; // 位置容差
        this.boardNode.children.forEach(function (child) {
            var childPos = child.getPosition();
            if (Math.abs(childPos.x - position.x) < tolerance &&
                Math.abs(childPos.y - position.y) < tolerance) {
                if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                    child.removeFromParent();
                }
            }
        });
    };
    /**
     * 清空所有预制体
     */
    SinglePlayerController.prototype.clearAllPrefabs = function () {
        this.boardNode.children.forEach(function (child) {
            if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                child.removeFromParent();
            }
        });
        // 重置格子数据
        var _a = this.currentMapConfig.boardDimensions, rows = _a.rows, cols = _a.cols;
        for (var x = 0; x < cols; x++) {
            for (var y = 0; y < rows; y++) {
                this.gridData[x][y].hasPlayer = false;
                this.gridData[x][y].isRevealed = false;
                this.gridData[x][y].isMarked = false;
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], SinglePlayerController.prototype, "boardNode", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SinglePlayerController.prototype, "boom8Prefab", void 0);
    SinglePlayerController = __decorate([
        ccclass
    ], SinglePlayerController);
    return SinglePlayerController;
}(cc.Component));
exports.default = SinglePlayerController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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