
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/SinglePlayerTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0348ePkuwdBkLQDsmntKZQp', 'SinglePlayerTest');
// scripts/level/SinglePlayerTest.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SinglePlayerTest = void 0;
var cc_1 = require("cc");
var SinglePlayerController_1 = require("./SinglePlayerController");
var ccclass = cc_1._decorator.ccclass, property = cc_1._decorator.property;
/**
 * 单机模式控制器测试脚本
 * 用于测试和演示SinglePlayerController的功能
 */
var SinglePlayerTest = /** @class */ (function (_super) {
    __extends(SinglePlayerTest, _super);
    function SinglePlayerTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.singlePlayerController = null;
        _this.testBoardNode = null;
        _this.test8x8Button = null;
        _this.test9x9Button = null;
        _this.test9x10Button = null;
        _this.test10x10Button = null;
        _this.clearButton = null;
        _this.statusLabel = null;
        return _this;
    }
    SinglePlayerTest.prototype.onLoad = function () {
        this.setupButtons();
        this.updateStatus("单机模式控制器测试就绪");
    };
    SinglePlayerTest.prototype.setupButtons = function () {
        var _this = this;
        if (this.test8x8Button) {
            this.test8x8Button.node.on('click', function () { return _this.testMapType("8x8"); }, this);
        }
        if (this.test9x9Button) {
            this.test9x9Button.node.on('click', function () { return _this.testMapType("9x9"); }, this);
        }
        if (this.test9x10Button) {
            this.test9x10Button.node.on('click', function () { return _this.testMapType("9x10"); }, this);
        }
        if (this.test10x10Button) {
            this.test10x10Button.node.on('click', function () { return _this.testMapType("10x10"); }, this);
        }
        if (this.clearButton) {
            this.clearButton.node.on('click', this.clearAllPrefabs, this);
        }
    };
    SinglePlayerTest.prototype.testMapType = function (mapType) {
        if (!this.singlePlayerController) {
            this.updateStatus("错误: SinglePlayerController 未配置");
            return;
        }
        if (!this.testBoardNode) {
            this.updateStatus("错误: 测试棋盘节点未配置");
            return;
        }
        try {
            // 设置棋盘节点
            this.singlePlayerController.setBoardNode(this.testBoardNode);
            // 设置地图类型
            this.singlePlayerController.setMapType(mapType);
            // 清空之前的预制体
            this.singlePlayerController.clearAllPrefabs();
            this.updateStatus("\u5DF2\u5207\u6362\u5230 " + mapType + " \u5730\u56FE\u6A21\u5F0F");
            console.log("\u6D4B\u8BD5: \u5207\u6362\u5230 " + mapType + " \u5730\u56FE\u6A21\u5F0F");
        }
        catch (error) {
            this.updateStatus("\u9519\u8BEF: " + error.message);
            console.error("测试失败:", error);
        }
    };
    SinglePlayerTest.prototype.clearAllPrefabs = function () {
        if (!this.singlePlayerController) {
            this.updateStatus("错误: SinglePlayerController 未配置");
            return;
        }
        try {
            this.singlePlayerController.clearAllPrefabs();
            this.updateStatus("已清空所有预制体");
            console.log("测试: 清空所有预制体");
        }
        catch (error) {
            this.updateStatus("\u9519\u8BEF: " + error.message);
            console.error("清空失败:", error);
        }
    };
    SinglePlayerTest.prototype.updateStatus = function (message) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("[SinglePlayerTest] " + message);
    };
    /**
     * 模拟后端响应测试
     */
    SinglePlayerTest.prototype.simulateBackendResponse = function (x, y, action, result) {
        if (!this.singlePlayerController) {
            console.error("SinglePlayerController 未配置");
            return;
        }
        // 模拟后端响应数据
        var mockResponse = {
            msgId: "LevelClickBlock",
            data: {
                x: x,
                y: y,
                action: action,
                result: result
            }
        };
        console.log("模拟后端响应:", mockResponse);
        // 直接调用消息处理方法（仅用于测试）
        // 注意：这里需要访问私有方法，实际使用中应该通过WebSocket接收消息
        // this.singlePlayerController['handleLevelClickBlockResponse'](mockResponse.data);
    };
    /**
     * 测试各种操作结果
     */
    SinglePlayerTest.prototype.runTestSuite = function () {
        var _this = this;
        console.log("开始运行测试套件...");
        // 测试挖掘操作 - 数字结果
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(0, 0, 1, 1);
            console.log("测试: 挖掘操作 - 显示数字1");
        }, 1);
        // 测试挖掘操作 - 炸弹结果
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(1, 0, 1, "mine");
            console.log("测试: 挖掘操作 - 显示炸弹");
        }, 2);
        // 测试标记操作
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(2, 0, 2, "marked");
            console.log("测试: 标记操作 - 显示标记");
        }, 3);
        // 测试取消标记操作
        this.scheduleOnce(function () {
            _this.simulateBackendResponse(2, 0, 2, "unmarked");
            console.log("测试: 取消标记操作 - 移除标记");
        }, 4);
        this.updateStatus("测试套件运行中...");
    };
    __decorate([
        property(SinglePlayerController_1.SinglePlayerController)
    ], SinglePlayerTest.prototype, "singlePlayerController", void 0);
    __decorate([
        property(cc_1.Node)
    ], SinglePlayerTest.prototype, "testBoardNode", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test8x8Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test9x9Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test9x10Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "test10x10Button", void 0);
    __decorate([
        property(cc_1.Button)
    ], SinglePlayerTest.prototype, "clearButton", void 0);
    __decorate([
        property(cc_1.Label)
    ], SinglePlayerTest.prototype, "statusLabel", void 0);
    SinglePlayerTest = __decorate([
        ccclass('SinglePlayerTest')
    ], SinglePlayerTest);
    return SinglePlayerTest;
}(cc_1.Component));
exports.SinglePlayerTest = SinglePlayerTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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