
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":7,"./assets/meshTools/BaseSDK":16,"./assets/meshTools/tools/MeshSdkApi":19,"./assets/meshTools/tools/Publish":3,"./assets/meshTools/tools/MeshSdk":17,"./assets/scripts/TipsDialogController":18,"./assets/scripts/ToastController":20,"./assets/scripts/GlobalManagerController":62,"./assets/scripts/bean/GameBean":26,"./assets/scripts/bean/GlobalBean":4,"./assets/scripts/bean/LanguageType":22,"./assets/scripts/bean/EnumBean":38,"./assets/scripts/common/GameData":23,"./assets/scripts/common/GameMgr":9,"./assets/scripts/common/GameTools":21,"./assets/scripts/common/MineConsole":24,"./assets/scripts/common/EventCenter":25,"./assets/scripts/game/CongratsDialogController":68,"./assets/scripts/game/GamePageController":31,"./assets/scripts/game/GameScoreController":29,"./assets/scripts/game/BtnController":28,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/game/Chess/HexChessBoardController":37,"./assets/scripts/game/Chess/ChessBoardController":34,"./assets/scripts/hall/HallCenterLayController":27,"./assets/scripts/hall/HallCreateRoomController":30,"./assets/scripts/hall/HallJoinRoomController":33,"./assets/scripts/hall/HallPageController":32,"./assets/scripts/hall/HallParentController":35,"./assets/scripts/hall/InfoDialogController":45,"./assets/scripts/hall/KickOutDialogController":36,"./assets/scripts/hall/LeaveDialogController":40,"./assets/scripts/hall/LevelSelectDemo":39,"./assets/scripts/hall/MatchParentController":44,"./assets/scripts/hall/PlayerLayoutController":41,"./assets/scripts/hall/SettingDialogController":42,"./assets/scripts/hall/TopUpDialogController":43,"./assets/scripts/hall/HallAutoController":49,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/scripts/hall/Level/LevelSelectExample":56,"./assets/scripts/hall/Level/LevelSelectPageController":46,"./assets/scripts/hall/Level/ScrollViewHelper":47,"./assets/scripts/hall/Level/LevelItemController":48,"./assets/scripts/level/LevelPageController":11,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/net/HttpManager":51,"./assets/scripts/net/HttpUtils":64,"./assets/scripts/net/IHttpMsgBody":52,"./assets/scripts/net/MessageBaseBean":50,"./assets/scripts/net/MessageId":53,"./assets/scripts/net/WebSocketManager":58,"./assets/scripts/net/WebSocketTool":54,"./assets/scripts/net/ErrorCode":61,"./assets/scripts/pfb/InfoItemController":12,"./assets/scripts/pfb/InfoItemOneController":57,"./assets/scripts/pfb/MatchItemController":55,"./assets/scripts/pfb/PlayerGameController ":69,"./assets/scripts/pfb/PlayerScoreController":63,"./assets/scripts/pfb/SeatItemController":59,"./assets/scripts/pfb/CongratsItemController":60,"./assets/scripts/start_up/StartUpPageController":14,"./assets/scripts/start_up/StartUpCenterController":76,"./assets/scripts/test/NoticeRoundStartTest":13,"./assets/scripts/util/AudioMgr":15,"./assets/scripts/util/BlockingQueue":65,"./assets/scripts/util/Config":67,"./assets/scripts/util/Dictionary":74,"./assets/scripts/util/LocalStorageManager":66,"./assets/scripts/util/NickNameLabel":72,"./assets/scripts/util/Tools":73,"./assets/scripts/util/AudioManager":77,"./assets/meshTools/MeshTools":70,"./assets/resources/i18n/zh_HK":8,"./assets/resources/i18n/en":71,"./assets/resources/i18n/zh_CN":75},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{"../Singleton":7},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"../../meshTools/Singleton":7,"../hall/HallAutoController":49},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":47},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":19,"./EventCenter":25,"./GameData":23,"./GameTools":21,"./MineConsole":24},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../net/MessageId":53,"../net/WebSocketManager":58,"../hall/LeaveDialogController":40,"../util/Tools":73,"../util/Config":67},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":9,"../common/EventCenter":25,"../net/MessageId":53},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"../common/GameMgr":9,"./StartUpCenterController":76},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"./Config":67,"./Dictionary":74},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{"./util/Config":67,"./util/Tools":73},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../MeshTools":70,"../BaseSDK":16,"../../scripts/net/MessageBaseBean":50,"../../scripts/common/GameMgr":9,"../../scripts/common/EventCenter":25,"MeshSdk":17},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/MeshTools":70,"../../meshTools/Singleton":7,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../../meshTools/Singleton":7,"./GameMgr":9},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{"../bean/GlobalBean":4,"../net/MessageId":53,"../net/WebSocketManager":58,"../ToastController":20,"./HallAutoController":49,"./HallCreateRoomController":30,"./HallJoinRoomController":33},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../util/AudioManager":77,"../util/Config":67,"../util/LocalStorageManager":66},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/PlayerScoreController":63},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/SeatItemController":59,"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../bean/GlobalBean":4,"../hall/LeaveDialogController":40,"../util/AudioManager":77,"../util/Config":67,"../util/Tools":73,"./CongratsDialogController":68,"./GameScoreController":29,"./Chess/ChessBoardController":34,"./Chess/HexChessBoardController":37,"../net/WebSocketManager":58,"../net/MessageId":53},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../bean/GlobalBean":4,"../common/GameMgr":9,"../net/MessageId":53,"../net/WebSocketManager":58,"../net/WebSocketTool":54,"../ToastController":20,"../util/AudioManager":77,"./HallParentController":35,"./InfoDialogController":45,"./KickOutDialogController":36,"./LeaveDialogController":40,"./Level/LevelSelectPageController":46,"./MatchParentController":44,"./SettingDialogController":42},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":69},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../bean/GlobalBean":4,"../common/GameMgr":9,"../net/MessageId":53,"../net/WebSocketManager":58,"../ToastController":20,"../util/Config":67,"../util/Tools":73,"./HallCenterLayController":27},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../net/MessageId":53,"../net/WebSocketManager":58,"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":69},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../common/GameMgr":9,"../net/MessageId":53,"../net/WebSocketManager":58,"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../util/AudioManager":77,"../util/Config":67,"../util/LocalStorageManager":66,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../common/GameMgr":9,"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../bean/GlobalBean":4,"../common/EventCenter":25,"../common/GameMgr":9,"../net/MessageBaseBean":50,"../pfb/MatchItemController":55,"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../../GlobalManagerController":62,"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{"./HttpUtils":64,"./MessageBaseBean":50,"./GameServerUrl":10,"../../meshTools/MeshTools":70,"../common/GameMgr":9,"../common/EventCenter":25},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"./MessageBaseBean":50,"./MessageId":53,"../util/Tools":73,"../../meshTools/Singleton":7,"../common/EventCenter":25,"../common/GameMgr":9},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{"../util/NickNameLabel":72,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../../meshTools/Singleton":7,"../common/EventCenter":25,"../common/GameMgr":9,"./WebSocketTool":54},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{"../util/NickNameLabel":72,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../../meshTools/tools/Publish":3,"../util/Config":67,"../util/NickNameLabel":72,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../meshTools/MeshTools":70,"../meshTools/tools/Publish":3,"./bean/EnumBean":38,"./bean/GlobalBean":4,"./bean/LanguageType":22,"./common/EventCenter":25,"./common/GameMgr":9,"./game/GamePageController":31,"./hall/HallPageController":32,"./level/LevelPageController":11,"./hall/TopUpDialogController":43,"./net/ErrorCode":61,"./net/GameServerUrl":10,"./net/MessageBaseBean":50,"./net/MessageId":53,"./net/WebSocketManager":58,"./net/WebSocketTool":54,"./start_up/StartUpPageController":14,"./TipsDialogController":18,"./ToastController":20,"./util/AudioMgr":15,"./util/Config":67},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../bean/GlobalBean":4,"../util/NickNameLabel":72,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../bean/GlobalBean":4,"../common/EventCenter":25,"../common/GameMgr":9,"../net/MessageBaseBean":50,"../pfb/CongratsItemController":60,"../util/Config":67,"../util/Tools":73},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"./tools/Publish":3},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"./AudioManager":77,"./Config":67},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"../common/EventCenter":25,"../common/GameMgr":9,"../net/MessageBaseBean":50,"../util/Config":67},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"./AudioMgr":15,"./LocalStorageManager":66},"path":"preview-scripts/assets/scripts/util/AudioManager.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    