
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":16,"./assets/meshTools/BaseSDK":6,"./assets/meshTools/tools/MeshSdkApi":78,"./assets/meshTools/tools/Publish":77,"./assets/meshTools/tools/MeshSdk":5,"./assets/scripts/TipsDialogController":22,"./assets/scripts/ToastController":17,"./assets/scripts/GlobalManagerController":15,"./assets/scripts/bean/GameBean":41,"./assets/scripts/bean/GlobalBean":4,"./assets/scripts/bean/LanguageType":20,"./assets/scripts/bean/EnumBean":28,"./assets/scripts/common/GameData":9,"./assets/scripts/common/GameMgr":21,"./assets/scripts/common/GameTools":18,"./assets/scripts/common/MineConsole":23,"./assets/scripts/common/EventCenter":19,"./assets/scripts/game/CongratsDialogController":24,"./assets/scripts/game/GamePageController":29,"./assets/scripts/game/GameScoreController":25,"./assets/scripts/game/BtnController":39,"./assets/scripts/game/Chess/GridController":3,"./assets/scripts/game/Chess/HexChessBoardController":56,"./assets/scripts/game/Chess/ChessBoardController":52,"./assets/scripts/hall/HallCenterLayController":31,"./assets/scripts/hall/HallCreateRoomController":30,"./assets/scripts/hall/HallJoinRoomController":27,"./assets/scripts/hall/HallPageController":32,"./assets/scripts/hall/HallParentController":36,"./assets/scripts/hall/InfoDialogController":37,"./assets/scripts/hall/KickOutDialogController":34,"./assets/scripts/hall/LeaveDialogController":33,"./assets/scripts/hall/LevelSelectDemo":35,"./assets/scripts/hall/MatchParentController":62,"./assets/scripts/hall/PlayerLayoutController":43,"./assets/scripts/hall/SettingDialogController":38,"./assets/scripts/hall/TopUpDialogController":47,"./assets/scripts/hall/HallAutoController":44,"./assets/scripts/hall/Level/LevelSelectController":8,"./assets/scripts/hall/Level/LevelSelectExample":40,"./assets/scripts/hall/Level/LevelSelectPageController":75,"./assets/scripts/hall/Level/ScrollViewHelper":45,"./assets/scripts/hall/Level/LevelItemController":42,"./assets/scripts/level/SinglePlayerController":11,"./assets/scripts/level/LevelPageController":48,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/net/HttpManager":46,"./assets/scripts/net/HttpUtils":72,"./assets/scripts/net/IHttpMsgBody":50,"./assets/scripts/net/MessageBaseBean":49,"./assets/scripts/net/MessageId":58,"./assets/scripts/net/WebSocketManager":51,"./assets/scripts/net/WebSocketTool":53,"./assets/scripts/net/ErrorCode":54,"./assets/scripts/pfb/InfoItemController":63,"./assets/scripts/pfb/InfoItemOneController":66,"./assets/scripts/pfb/MatchItemController":12,"./assets/scripts/pfb/PlayerGameController ":55,"./assets/scripts/pfb/PlayerScoreController":59,"./assets/scripts/pfb/SeatItemController":57,"./assets/scripts/pfb/CongratsItemController":73,"./assets/scripts/start_up/StartUpPageController":60,"./assets/scripts/start_up/StartUpCenterController":13,"./assets/scripts/test/NoticeRoundStartTest":26,"./assets/scripts/util/AudioMgr":14,"./assets/scripts/util/BlockingQueue":64,"./assets/scripts/util/Config":61,"./assets/scripts/util/Dictionary":74,"./assets/scripts/util/LocalStorageManager":76,"./assets/scripts/util/NickNameLabel":70,"./assets/scripts/util/Tools":69,"./assets/scripts/util/AudioManager":65,"./assets/meshTools/MeshTools":68,"./assets/resources/i18n/zh_HK":7,"./assets/resources/i18n/en":71,"./assets/resources/i18n/zh_CN":67},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"../../meshTools/Singleton":16,"../hall/HallAutoController":44},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{"./ScrollViewHelper":45},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{"../../meshTools/MeshTools":68,"../../meshTools/Singleton":16,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../net/MessageId":58,"../net/WebSocketManager":51,"../common/GameMgr":21,"../common/EventCenter":19},"path":"preview-scripts/assets/scripts/level/SinglePlayerController.js"},{"deps":{"../util/NickNameLabel":70,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../common/EventCenter":19,"../common/GameMgr":21,"../net/MessageBaseBean":49,"../util/Config":61},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"./Config":61,"./Dictionary":74},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"../meshTools/MeshTools":68,"../meshTools/tools/Publish":77,"./bean/EnumBean":28,"./bean/GlobalBean":4,"./bean/LanguageType":20,"./common/EventCenter":19,"./common/GameMgr":21,"./game/GamePageController":29,"./hall/HallPageController":32,"./level/LevelPageController":48,"./hall/TopUpDialogController":47,"./net/ErrorCode":54,"./net/GameServerUrl":10,"./net/MessageBaseBean":49,"./net/MessageId":58,"./net/WebSocketManager":51,"./net/WebSocketTool":53,"./start_up/StartUpPageController":60,"./TipsDialogController":22,"./ToastController":17,"./util/AudioMgr":14,"./util/Config":61},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"../../meshTools/Singleton":16},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../../meshTools/Singleton":16,"./GameMgr":21},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":78,"./EventCenter":19,"./GameData":9,"./GameTools":18,"./MineConsole":23},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"./util/Config":61,"./util/Tools":69},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../../meshTools/Singleton":16},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../bean/GlobalBean":4,"../common/EventCenter":19,"../common/GameMgr":21,"../net/MessageBaseBean":49,"../pfb/CongratsItemController":73,"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/PlayerScoreController":59},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../common/GameMgr":21,"../common/EventCenter":19,"../net/MessageId":58},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../bean/GlobalBean":4,"../hall/LeaveDialogController":33,"../util/AudioManager":65,"../util/Config":61,"../util/Tools":69,"./CongratsDialogController":24,"./GameScoreController":25,"./Chess/ChessBoardController":52,"./Chess/HexChessBoardController":56,"../net/WebSocketManager":51,"../net/MessageId":58},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/SeatItemController":57,"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../bean/GlobalBean":4,"../net/MessageId":58,"../net/WebSocketManager":51,"../ToastController":17,"./HallAutoController":44,"./HallCreateRoomController":30,"./HallJoinRoomController":27},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../bean/GlobalBean":4,"../common/GameMgr":21,"../net/MessageId":58,"../net/WebSocketManager":51,"../net/WebSocketTool":53,"../ToastController":17,"../util/AudioManager":65,"./HallParentController":36,"./InfoDialogController":37,"./KickOutDialogController":34,"./LeaveDialogController":33,"./Level/LevelSelectPageController":75,"./MatchParentController":62,"./SettingDialogController":38},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../common/GameMgr":21,"../net/MessageId":58,"../net/WebSocketManager":51,"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../net/MessageId":58,"../net/WebSocketManager":51,"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"./Level/LevelSelectController":8},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../../meshTools/tools/Publish":77,"../bean/GlobalBean":4,"../common/GameMgr":21,"../net/MessageId":58,"../net/WebSocketManager":51,"../ToastController":17,"../util/Config":61,"../util/Tools":69,"./HallCenterLayController":31},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../../meshTools/tools/Publish":77,"../util/AudioManager":65,"../util/Config":61,"../util/LocalStorageManager":76,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../util/AudioManager":65,"../util/Config":61,"../util/LocalStorageManager":76},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"./LevelSelectController":8},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{"./LevelSelectController":8},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./HttpUtils":72,"./MessageBaseBean":49,"./GameServerUrl":10,"../../meshTools/MeshTools":68,"../common/GameMgr":21,"../common/EventCenter":19},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{"../common/GameMgr":21,"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../net/MessageId":58,"../net/WebSocketManager":51,"../hall/LeaveDialogController":33,"../util/Tools":69,"../util/Config":61,"./SinglePlayerController":11},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"../../meshTools/Singleton":16,"../common/EventCenter":19,"../common/GameMgr":21,"./WebSocketTool":53},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":55},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"./MessageBaseBean":49,"./MessageId":58,"../util/Tools":69,"../../meshTools/Singleton":16,"../common/EventCenter":19,"../common/GameMgr":21},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":55},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../util/NickNameLabel":70,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"../bean/GlobalBean":4,"../util/NickNameLabel":70,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"../common/GameMgr":21,"./StartUpCenterController":13},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../../meshTools/tools/Publish":77,"../bean/GlobalBean":4,"../common/EventCenter":19,"../common/GameMgr":21,"../net/MessageBaseBean":49,"../pfb/MatchItemController":12,"../util/Config":61,"../util/Tools":69},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"./AudioMgr":14,"./LocalStorageManager":76},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"./tools/Publish":77},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{"./AudioManager":65,"./Config":61},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{"../../meshTools/tools/Publish":77,"../util/Config":61,"../util/NickNameLabel":70,"../util/Tools":69},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{"../../GlobalManagerController":15,"./LevelSelectController":8},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"../../meshTools/Singleton":16},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{"../Singleton":16},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"../MeshTools":68,"../BaseSDK":6,"MeshSdk":5,"../../scripts/common/EventCenter":19,"../../scripts/common/GameMgr":21,"../../scripts/net/MessageBaseBean":49},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    