{"version": 3, "sources": ["assets/scripts/level/SinglePlayerController.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4DAA2D;AAC3D,8CAA6C;AAE7C,qDAAkD;AAClD,6CAA4C;AAEtC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAmB5C;IAAoD,0CAAY;IAAhE;QAAA,qEA4dC;QAzdG,eAAS,GAAY,IAAI,CAAC,CAAC,OAAO;QAGlC,gBAAU,GAAc,IAAI,CAAC,CAAC,QAAQ;QAGtC,kBAAY,GAAc,IAAI,CAAC,CAAC,QAAQ;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,OAAO;QACC,gBAAU,GAAiC;YAC/C,KAAK,EAAE;gBACH,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;aACxC;YACD,KAAK,EAAE;gBACH,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;aACxC;YACD,MAAM,EAAE;gBACJ,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;aACzC;YACD,OAAO,EAAE;gBACL,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;aAC1C;SACJ,CAAC;QAEF,SAAS;QACD,sBAAgB,GAAc,IAAI,CAAC;QACnC,oBAAc,GAAW,KAAK,CAAC,CAAC,QAAQ;QAEhD,SAAS;QACD,cAAQ,GAAiB,EAAE,CAAC;QAC5B,eAAS,GAAgB,EAAE,CAAC;;IAkaxC,CAAC;IAhaG,uCAAM,GAAN;QACI,SAAS;QACT,iBAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAEtF,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAED,0CAAS,GAAT;QACI,SAAS;QACT,iBAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACI,2CAAU,GAAjB,UAAkB,OAAe;QAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,iDAAY,OAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE1D,UAAU;YACV,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,EAAE,CAAC;aACpB;SACJ;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,uDAAa,OAAS,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;;OAGG;IACI,6CAAY,GAAnB,UAAoB,SAAkB;QAClC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,SAAS,EAAE,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACK,0CAAS,GAAjB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC3C,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YAC9B,OAAO;SACV;QAEK,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;QAE7D,UAAU;QACV,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;oBAClB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,KAAK;oBACjB,QAAQ,EAAE,KAAK;iBAClB,CAAC;aACL;SACJ;QAED,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,4DAA2B,GAAnC;QAAA,iBAmBC;QAlBG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAEK,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;QAE7D,uBAAuB;QACvB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAc,EAAE,KAAa;YAC1D,iBAAiB;YACjB,IAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;YACvB,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE;gBACtB,KAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtC,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;aAChC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,oDAAmB,GAA3B,UAA4B,QAAiB,EAAE,CAAS,EAAE,CAAS;QAAnE,iBAgDC;QA/CG,SAAS;QACT,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,iBAAiB,GAAa,IAAI,CAAC;QACvC,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,SAAS;QAEtC,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAC,MAA2B;YACnE,cAAc,GAAG,IAAI,CAAC;YACtB,cAAc,GAAG,CAAC,CAAC;YAEnB,SAAS;YACT,iBAAiB,GAAG;gBAChB,IAAI,cAAc,EAAE;oBAChB,cAAc,IAAI,GAAG,CAAC;oBACtB,IAAI,cAAc,IAAI,eAAe,EAAE;wBACnC,KAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAC3B,cAAc,GAAG,KAAK,CAAC;wBACvB,IAAI,iBAAiB,EAAE;4BACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;yBACtC;qBACJ;iBACJ;YACL,CAAC,CAAC;YACF,KAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,MAA2B;YACjE,iBAAiB;YACjB,IAAI,cAAc,IAAI,cAAc,GAAG,eAAe,EAAE;gBACpD,KAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC1B;YAED,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAC,MAA2B;YACpE,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACK,4CAAW,GAAnB,UAAoB,CAAS,EAAE,CAAS;QACpC,OAAO,CAAC,GAAG,CAAC,gCAAU,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;QAElC,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QAED,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,EAAE;YAC3C,OAAO;SACV;QAED,YAAY;QACZ,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,gDAAe,GAAvB,UAAwB,CAAS,EAAE,CAAS;QACxC,OAAO,CAAC,GAAG,CAAC,gCAAU,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;QAElC,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QAED,eAAe;QACf,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,QAAQ,CAAC,UAAU,EAAE;YACrB,OAAO;SACV;QAED,YAAY;QACZ,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,oDAAmB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC5D,IAAM,SAAS,GAAsB;YACjC,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,MAAM,CAAC,qBAAqB;SACvC,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,0CAAsB,EAAE,SAAS,CAAC,CAAC;QAC/C,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACK,iDAAgB,GAAxB,UAAyB,IAAS;QAC9B,IAAI,IAAI,CAAC,KAAK,KAAK,qBAAS,CAAC,sBAAsB,EAAE;YACjD,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjD;IACL,CAAC;IAED;;OAEG;IACK,8DAA6B,GAArC,UAAsC,YAAqC;QACvE,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;QAE1C,IAAA,CAAC,GAAwB,YAAY,EAApC,EAAE,CAAC,GAAqB,YAAY,EAAjC,EAAE,MAAM,GAAa,YAAY,OAAzB,EAAE,MAAM,GAAK,YAAY,OAAjB,CAAkB;QAE9C,SAAS;QACT,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAErC,IAAI,MAAM,KAAK,CAAC,EAAE;gBACd,OAAO;gBACP,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;gBAC3B,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;gBAE1B,aAAa;gBACb,IAAI,MAAM,KAAK,MAAM,EAAE;oBACnB,OAAO;oBACP,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC/B;qBAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBACnC,OAAO;oBACP,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;iBACzC;aACJ;iBAAM,IAAI,MAAM,KAAK,CAAC,EAAE;gBACrB,OAAO;gBACP,IAAI,MAAM,KAAK,QAAQ,EAAE;oBACrB,OAAO;oBACP,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACzB,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;oBAC1B,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACjC;qBAAM,IAAI,MAAM,KAAK,UAAU,EAAE;oBAC9B,OAAO;oBACP,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;oBAC1B,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;oBAC3B,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC7B;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACK,kDAAiB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QACpC,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;QAC7D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,wDAAuB,GAA/B,UAAgC,CAAS,EAAE,CAAS;QAC1C,IAAA,KAA0B,IAAI,CAAC,gBAAgB,EAA7C,SAAS,eAAA,EAAE,QAAQ,cAA0B,CAAC;QAEtD,cAAc;QACd,IAAM,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC7D,IAAM,MAAM,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE/D,SAAS;QACT,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE9C,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,iDAAgB,GAAxB,UAAyB,CAAS,EAAE,CAAS;QACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,IAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;QAEvB,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAElC,SAAS;QACT,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACb,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;aACrC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,mDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS;QAC3C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACrC,OAAO;SACV;QAED,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;QAE3B,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEpC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,mDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC3D,IAAI,MAAM,KAAK,CAAC,EAAE;YACd,OAAO,CAAC,SAAS;SACpB;QAED,IAAI,MAAM,GAAc,IAAI,CAAC;QAC7B,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC;gBACI,OAAO,CAAC,KAAK,CAAC,2CAAW,MAAQ,CAAC,CAAC;gBACnC,OAAO;SACd;QAED,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,SAAO,MAAM,gDAAe,CAAC,CAAC;YAC5C,OAAO;SACV;QAED,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,CAAC,IAAI,GAAG,SAAO,MAAQ,CAAC;QAElC,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEpC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,+CAAc,GAAtB,UAAuB,CAAS,EAAE,CAAS;QACvC,eAAe;QACf,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,IAAM,SAAS,GAAG,EAAE,CAAC,CAAC,OAAO;QAE7B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAc;YAC3C,IAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS;gBAC7C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE;gBAC/C,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;oBAC9D,KAAK,CAAC,gBAAgB,EAAE,CAAC;iBAC5B;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,gDAAe,GAAtB;QACI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAc;YAC3C,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAC9D,KAAK,CAAC,gBAAgB,EAAE,CAAC;aAC5B;QACL,CAAC,CAAC,CAAC;QAEH,SAAS;QACH,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;QAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;gBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC;gBACvC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;aACxC;SACJ;IACL,CAAC;IAxdD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACQ;IAG1B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;8DACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACW;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IA1Bb,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CA4d1C;IAAD,6BAAC;CA5dD,AA4dC,CA5dmD,EAAE,CAAC,SAAS,GA4d/D;kBA5doB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { WebSocketManager } from '../net/WebSocketManager';\nimport { MessageId } from '../net/MessageId';\nimport { ClickBlockRequest, LevelClickBlockResponse } from '../bean/GameBean';\nimport { EventType } from '../common/EventCenter';\nimport { GameMgr } from '../common/GameMgr';\n\nconst { ccclass, property } = cc._decorator;\n\n// 地图配置接口\ninterface MapConfig {\n    boardSize: { width: number, height: number };  // 棋盘大小\n    gridSize: { width: number, height: number };   // 格子大小\n    boardDimensions: { rows: number, cols: number }; // 行列数\n}\n\n// 格子数据接口\ninterface GridData {\n    x: number;\n    y: number;\n    hasPlayer: boolean;\n    isRevealed: boolean;\n    isMarked: boolean;\n}\n\n@ccclass\nexport default class SinglePlayerController extends cc.Component {\n\n    @property(cc.Node)\n    boardNode: cc.Node = null; // 棋盘节点\n\n    @property(cc.Prefab)\n    boomPrefab: cc.Prefab = null; // 炸弹预制体\n\n    @property(cc.Prefab)\n    biaojiPrefab: cc.Prefab = null; // 标记预制体\n\n    @property(cc.Prefab)\n    boom1Prefab: cc.Prefab = null; // 数字1预制体\n    @property(cc.Prefab)\n    boom2Prefab: cc.Prefab = null; // 数字2预制体\n    @property(cc.Prefab)\n    boom3Prefab: cc.Prefab = null; // 数字3预制体\n    @property(cc.Prefab)\n    boom4Prefab: cc.Prefab = null; // 数字4预制体\n    @property(cc.Prefab)\n    boom5Prefab: cc.Prefab = null; // 数字5预制体\n    @property(cc.Prefab)\n    boom6Prefab: cc.Prefab = null; // 数字6预制体\n    @property(cc.Prefab)\n    boom7Prefab: cc.Prefab = null; // 数字7预制体\n    @property(cc.Prefab)\n    boom8Prefab: cc.Prefab = null; // 数字8预制体\n\n    // 地图配置\n    private mapConfigs: { [key: string]: MapConfig } = {\n        \"8x8\": {\n            boardSize: { width: 752, height: 845 },\n            gridSize: { width: 88, height: 88 },\n            boardDimensions: { rows: 8, cols: 8 }\n        },\n        \"9x9\": {\n            boardSize: { width: 752, height: 747 },\n            gridSize: { width: 76, height: 76 },\n            boardDimensions: { rows: 9, cols: 9 }\n        },\n        \"9x10\": {\n            boardSize: { width: 752, height: 830 },\n            gridSize: { width: 78, height: 78 },\n            boardDimensions: { rows: 9, cols: 10 }\n        },\n        \"10x10\": {\n            boardSize: { width: 752, height: 745 },\n            gridSize: { width: 69, height: 69 },\n            boardDimensions: { rows: 10, cols: 10 }\n        }\n    };\n\n    // 当前地图配置\n    private currentMapConfig: MapConfig = null;\n    private currentMapType: string = \"8x8\"; // 默认8x8\n\n    // 格子数据存储\n    private gridData: GridData[][] = [];\n    private gridNodes: cc.Node[][] = [];\n\n    onLoad() {\n        // 注册消息监听\n        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n\n        // 设置默认地图配置\n        this.setMapType(this.currentMapType);\n        this.initBoard();\n    }\n\n    onDestroy() {\n        // 取消消息监听\n        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n    }\n\n    /**\n     * 设置地图类型\n     * @param mapType 地图类型 \"8x8\", \"9x9\", \"9x10\", \"10x10\"\n     */\n    public setMapType(mapType: string) {\n        if (this.mapConfigs[mapType]) {\n            this.currentMapType = mapType;\n            this.currentMapConfig = this.mapConfigs[mapType];\n            console.log(`设置地图类型为: ${mapType}`, this.currentMapConfig);\n\n            // 重新初始化棋盘\n            if (this.boardNode) {\n                this.initBoard();\n            }\n        } else {\n            console.error(`不支持的地图类型: ${mapType}`);\n        }\n    }\n\n    /**\n     * 设置棋盘节点\n     * @param boardNode 棋盘节点\n     */\n    public setBoardNode(boardNode: cc.Node) {\n        this.boardNode = boardNode;\n        if (this.currentMapConfig) {\n            this.initBoard();\n        }\n    }\n\n    /**\n     * 初始化棋盘\n     */\n    private initBoard() {\n        if (!this.currentMapConfig || !this.boardNode) {\n            console.error(\"地图配置或棋盘节点未设置\");\n            return;\n        }\n\n        const { rows, cols } = this.currentMapConfig.boardDimensions;\n        \n        // 初始化格子数据\n        this.gridData = [];\n        this.gridNodes = [];\n        \n        for (let x = 0; x < cols; x++) {\n            this.gridData[x] = [];\n            this.gridNodes[x] = [];\n            for (let y = 0; y < rows; y++) {\n                this.gridData[x][y] = {\n                    x: x,\n                    y: y,\n                    hasPlayer: false,\n                    isRevealed: false,\n                    isMarked: false\n                };\n            }\n        }\n\n        this.enableTouchForExistingGrids();\n    }\n\n    /**\n     * 为现有格子启用触摸事件\n     */\n    private enableTouchForExistingGrids() {\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法启用触摸事件！\");\n            return;\n        }\n\n        const { rows, cols } = this.currentMapConfig.boardDimensions;\n\n        // 遍历棋盘的所有子节点，为格子添加触摸事件\n        this.boardNode.children.forEach((child: cc.Node, index: number) => {\n            // 根据子节点的索引计算格子坐标\n            const x = index % cols;\n            const y = Math.floor(index / cols);\n\n            if (x < cols && y < rows) {\n                this.addTouchEventToGrid(child, x, y);\n                this.gridNodes[x][y] = child;\n            }\n        });\n    }\n\n    /**\n     * 为格子添加触摸事件\n     */\n    private addTouchEventToGrid(gridNode: cc.Node, x: number, y: number) {\n        // 长按相关变量\n        let isLongPressing = false;\n        let longPressTimer = 0;\n        let longPressCallback: Function = null;\n        const LONG_PRESS_TIME = 1.0; // 1秒长按时间\n\n        // 触摸开始事件\n        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {\n            isLongPressing = true;\n            longPressTimer = 0;\n\n            // 开始长按检测\n            longPressCallback = () => {\n                if (isLongPressing) {\n                    longPressTimer += 0.1;\n                    if (longPressTimer >= LONG_PRESS_TIME) {\n                        this.onGridLongPress(x, y);\n                        isLongPressing = false;\n                        if (longPressCallback) {\n                            this.unschedule(longPressCallback);\n                        }\n                    }\n                }\n            };\n            this.schedule(longPressCallback, 0.1);\n        }, this);\n\n        // 触摸结束事件\n        gridNode.on(cc.Node.EventType.TOUCH_END, (_event: cc.Event.EventTouch) => {\n            // 如果不是长按，则执行点击事件\n            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {\n                this.onGridClick(x, y);\n            }\n\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n\n        // 触摸取消事件\n        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n    }\n\n    /**\n     * 格子点击事件 - 发送挖掘操作\n     */\n    private onGridClick(x: number, y: number) {\n        console.log(`格子点击: (${x}, ${y})`);\n\n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n            return;\n        }\n\n        // 检查该位置是否已经被揭开或标记\n        const gridData = this.gridData[x][y];\n        if (gridData.isRevealed || gridData.hasPlayer) {\n            return;\n        }\n\n        // 发送挖掘操作到后端\n        this.sendLevelClickBlock(x, y, 1);\n    }\n\n    /**\n     * 格子长按事件 - 发送标记操作\n     */\n    private onGridLongPress(x: number, y: number) {\n        console.log(`格子长按: (${x}, ${y})`);\n\n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n            return;\n        }\n\n        // 检查该位置是否已经被揭开\n        const gridData = this.gridData[x][y];\n        if (gridData.isRevealed) {\n            return;\n        }\n\n        // 发送标记操作到后端\n        this.sendLevelClickBlock(x, y, 2);\n    }\n\n    /**\n     * 发送关卡点击方块消息\n     */\n    private sendLevelClickBlock(x: number, y: number, action: number) {\n        const clickData: ClickBlockRequest = {\n            x: x,\n            y: y,\n            action: action // 1=挖掘方块，2=标记/取消标记地雷\n        };\n\n        console.log(`发送LevelClickBlock消息:`, clickData);\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLevelClickBlock, clickData);\n    }\n\n    /**\n     * 处理后端消息\n     */\n    private onReceiveMessage(data: any) {\n        if (data.msgId === MessageId.MsgTypeLevelClickBlock) {\n            this.handleLevelClickBlockResponse(data.data);\n        }\n    }\n\n    /**\n     * 处理关卡点击方块响应\n     */\n    private handleLevelClickBlockResponse(responseData: LevelClickBlockResponse) {\n        console.log(\"收到LevelClickBlock响应:\", responseData);\n\n        const { x, y, result, action } = responseData;\n        \n        // 更新格子状态\n        if (this.isValidCoordinate(x, y)) {\n            const gridData = this.gridData[x][y];\n            \n            if (action === 1) {\n                // 挖掘操作\n                gridData.isRevealed = true;\n                gridData.hasPlayer = true;\n                \n                // 根据结果显示相应内容\n                if (result === \"mine\") {\n                    // 显示炸弹\n                    this.createBoomPrefab(x, y);\n                } else if (typeof result === \"number\") {\n                    // 显示数字\n                    this.createNumberPrefab(x, y, result);\n                }\n            } else if (action === 2) {\n                // 标记操作\n                if (result === \"marked\") {\n                    // 添加标记\n                    gridData.isMarked = true;\n                    gridData.hasPlayer = true;\n                    this.createBiaojiPrefab(x, y);\n                } else if (result === \"unmarked\") {\n                    // 取消标记\n                    gridData.isMarked = false;\n                    gridData.hasPlayer = false;\n                    this.removePrefabAt(x, y);\n                }\n            }\n        }\n    }\n\n    /**\n     * 检查坐标是否有效\n     */\n    private isValidCoordinate(x: number, y: number): boolean {\n        const { rows, cols } = this.currentMapConfig.boardDimensions;\n        return x >= 0 && x < cols && y >= 0 && y < rows;\n    }\n\n    /**\n     * 计算预制体的精确位置\n     */\n    private calculatePrefabPosition(x: number, y: number): cc.Vec2 {\n        const { boardSize, gridSize } = this.currentMapConfig;\n\n        // 计算起始位置（左下角）\n        const startX = -(boardSize.width / 2) + (gridSize.width / 2);\n        const startY = -(boardSize.height / 2) + (gridSize.height / 2);\n\n        // 计算最终位置\n        const finalX = startX + (x * gridSize.width);\n        const finalY = startY + (y * gridSize.height);\n\n        return cc.v2(finalX, finalY);\n    }\n\n    /**\n     * 在指定位置创建炸弹预制体\n     */\n    private createBoomPrefab(x: number, y: number) {\n        if (!this.boomPrefab) {\n            console.error(\"boomPrefab 预制体未设置\");\n            return;\n        }\n\n        const boomNode = cc.instantiate(this.boomPrefab);\n        boomNode.name = \"Boom\";\n\n        const position = this.calculatePrefabPosition(x, y);\n        boomNode.setPosition(position);\n\n        this.boardNode.addChild(boomNode);\n\n        // 播放出现动画\n        boomNode.setScale(0);\n        cc.tween(boomNode)\n            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })\n            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })\n            .start();\n    }\n\n    /**\n     * 在指定位置创建标记预制体\n     */\n    private createBiaojiPrefab(x: number, y: number) {\n        if (!this.biaojiPrefab) {\n            console.error(\"biaojiPrefab 预制体未设置\");\n            return;\n        }\n\n        const biaojiNode = cc.instantiate(this.biaojiPrefab);\n        biaojiNode.name = \"Biaoji\";\n\n        const position = this.calculatePrefabPosition(x, y);\n        biaojiNode.setPosition(position);\n\n        this.boardNode.addChild(biaojiNode);\n\n        // 播放出现动画\n        biaojiNode.setScale(0);\n        cc.tween(biaojiNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n    }\n\n    /**\n     * 在指定位置创建数字预制体\n     */\n    private createNumberPrefab(x: number, y: number, number: number) {\n        if (number === 0) {\n            return; // 0不需要显示\n        }\n\n        let prefab: cc.Prefab = null;\n        switch (number) {\n            case 1: prefab = this.boom1Prefab; break;\n            case 2: prefab = this.boom2Prefab; break;\n            case 3: prefab = this.boom3Prefab; break;\n            case 4: prefab = this.boom4Prefab; break;\n            case 5: prefab = this.boom5Prefab; break;\n            case 6: prefab = this.boom6Prefab; break;\n            case 7: prefab = this.boom7Prefab; break;\n            case 8: prefab = this.boom8Prefab; break;\n            default:\n                console.error(`不支持的数字: ${number}`);\n                return;\n        }\n\n        if (!prefab) {\n            console.error(`boom${number}Prefab 预制体未设置`);\n            return;\n        }\n\n        const numberNode = cc.instantiate(prefab);\n        numberNode.name = `Boom${number}`;\n\n        const position = this.calculatePrefabPosition(x, y);\n        numberNode.setPosition(position);\n\n        this.boardNode.addChild(numberNode);\n\n        // 播放出现动画\n        numberNode.setScale(0);\n        cc.tween(numberNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n    }\n\n    /**\n     * 移除指定位置的预制体\n     */\n    private removePrefabAt(x: number, y: number) {\n        // 查找并移除该位置的预制体\n        const position = this.calculatePrefabPosition(x, y);\n        const tolerance = 10; // 位置容差\n\n        this.boardNode.children.forEach((child: cc.Node) => {\n            const childPos = child.getPosition();\n            if (Math.abs(childPos.x - position.x) < tolerance &&\n                Math.abs(childPos.y - position.y) < tolerance) {\n                if (child.name.includes(\"Biaoji\") || child.name.includes(\"Boom\")) {\n                    child.removeFromParent();\n                }\n            }\n        });\n    }\n\n    /**\n     * 清空所有预制体\n     */\n    public clearAllPrefabs() {\n        this.boardNode.children.forEach((child: cc.Node) => {\n            if (child.name.includes(\"Biaoji\") || child.name.includes(\"Boom\")) {\n                child.removeFromParent();\n            }\n        });\n\n        // 重置格子数据\n        const { rows, cols } = this.currentMapConfig.boardDimensions;\n        for (let x = 0; x < cols; x++) {\n            for (let y = 0; y < rows; y++) {\n                this.gridData[x][y].hasPlayer = false;\n                this.gridData[x][y].isRevealed = false;\n                this.gridData[x][y].isMarked = false;\n            }\n        }\n    }\n}\n"]}