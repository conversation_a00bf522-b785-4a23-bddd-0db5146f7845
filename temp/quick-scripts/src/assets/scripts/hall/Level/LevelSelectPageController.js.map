{"version": 3, "sources": ["assets/scripts/hall/Level/LevelSelectPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,yEAAkF;AAClF,iEAA6E;AAEvE,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAuD,6CAAY;IAAnE;QAAA,qEAiNC;QA9MG,2BAAqB,GAA0B,IAAI,CAAC;QAGpD,gBAAU,GAAkB,IAAI,CAAC;QAIjC,UAAU;QAEV,qBAAe,GAAc,IAAI,CAAC;QAGlC,kBAAY,GAAc,IAAI,CAAC;;IAkMnC,CAAC;IAhMG,0CAAM,GAAN;QACI,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,iCAAiC;QACjC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;SACtE;IACL,CAAC;IAED,yCAAK,GAAL;QAAA,iBAYC;QAXG,aAAa;QACb,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,GAAG,UAAC,WAAmB;gBACrE,KAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC,CAAC;SACL;QAED,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,mDAAe,GAAvB;QACI,WAAW;QACX,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAID;;OAEG;IACI,sDAAkB,GAAzB;QACI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAM,eAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YAG3E,gBAAgB;YAChB,IAAI,eAAa,GAA4B,IAAI,CAAC;YAElD,gCAAgC;YAChC,IAAI,iBAAiB,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC/C,IAAI,iBAAiB,EAAE;gBACnB,eAAa,GAAG,iBAAiB,CAAC,YAAY,CAAC,iCAAuB,CAAC,CAAC;aAE3E;YAED,oCAAoC;YACpC,IAAI,CAAC,eAAa,EAAE;gBAChB,iBAAiB,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAClD,IAAI,iBAAiB,EAAE;oBAEnB,eAAa,GAAG,iBAAiB,CAAC,YAAY,CAAC,iCAAuB,CAAC,CAAC;oBACxE,IAAI,eAAa,EAAE;qBAElB;yBAAM;wBAEH,aAAa;wBACb,IAAM,UAAU,GAAG,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;qBAEpE;iBACJ;aAEJ;YAKD,IAAI,eAAa,EAAE;gBACf,WAAW;gBACX,eAAa,CAAC,cAAc,CAAC,kCAAQ,CAAC,UAAU,CAAC,CAAC;gBAElD,sBAAsB;gBACtB,IAAI,CAAC,YAAY,CAAC;oBACd,IAAI,eAAa,CAAC,mBAAmB,EAAE;wBAEnC,eAAa,CAAC,mBAAmB,CAAC,eAAe,CAAC,eAAa,CAAC,CAAC;qBACpE;gBACL,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;iBAAM;gBACH,EAAE,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBACrD,EAAE,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;aAGxD;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,oDAAgB,GAAvB,UAAwB,iBAAsB;QAA9C,iBAUC;QATG,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QAExC,uCAAuC;QACvC,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAE/D,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,mBAAmB;IACjC,CAAC;IAED;;OAEG;IACK,0DAAsB,GAA9B;QACI,qCAAqC;QACrC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC5C;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE;YACrE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;YACjE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAClE;IACL,CAAC;IAED;;OAEG;IACK,qDAAiB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QAExC,IAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;QAC1E,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAExE,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,gBAAgB;QAChB,IAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,CAAC;QAEzD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC;YAC7C,sBAAsB;YACtB,IAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAC1E,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;aAC/B;SACJ;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACK,0DAAsB,GAA9B;QACI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO;QAExC,IAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;QAC1E,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAExE,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,EAAE;YAEtD,cAAc;YACd,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACK,uDAAmB,GAA3B;QACI,IAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;QAE1E,qBAAqB;QACrB,YAAY;IAChB,CAAC;IAED;;OAEG;IACK,2DAAuB,GAA/B,UAAgC,WAAmB;QAE/C,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IA7MD;QADC,QAAQ,CAAC,+BAAqB,CAAC;4EACoB;IAGpD;QADC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC;iEACS;IAMjC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;sEACc;IAGlC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;mEACW;IAfd,yBAAyB;QAD7C,OAAO;OACa,yBAAyB,CAiN7C;IAAD,gCAAC;CAjND,AAiNC,CAjNsD,EAAE,CAAC,SAAS,GAiNlE;kBAjNoB,yBAAyB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport GlobalManagerController, { PageType } from \"../../GlobalManagerController\";\nimport LevelSelectController, { LevelStatus } from \"./LevelSelectController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LevelSelectPageController extends cc.Component {\n\n    @property(LevelSelectController)\n    levelSelectController: LevelSelectController = null;\n\n    @property(cc.ScrollView)\n    scrollView: cc.ScrollView = null;\n\n\n\n    // 新增的游戏按钮\n    @property(cc.Button)\n    startGameButton: cc.Button = null;\n\n    @property(cc.Button)\n    lockedButton: cc.Button = null;\n\n    onLoad() {\n        // 修复ScrollView的Scrollbar问题\n        this.fixScrollViewScrollbar();\n\n        // 设置按钮初始状态 - 默认显示开始游戏按钮，避免状态切换闪烁\n        if (this.startGameButton) {\n            this.startGameButton.node.active = true;\n            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);\n        }\n        if (this.lockedButton) {\n            this.lockedButton.node.active = false;\n            this.lockedButton.node.on('click', this.onLockedButtonClick, this);\n        }\n    }\n\n    start() {\n        // 设置关卡选择变化回调\n        if (this.levelSelectController) {\n            this.levelSelectController.onLevelSelectionChanged = (levelNumber: number) => {\n                this.onLevelSelectionChanged(levelNumber);\n            };\n        }\n\n        // 延迟更新UI，确保在关卡数据加载后再更新按钮状态\n        this.scheduleOnce(() => {\n            this.updateUIDisplay();\n        }, 0.1);\n    }\n\n    /**\n     * 更新UI显示状态\n     */\n    private updateUIDisplay() {\n        // 更新游戏按钮状态\n        this.updateGameButtons();\n    }\n\n\n\n    /**\n     * 进入选中的关卡\n     */\n    public enterSelectedLevel() {\n        if (this.levelSelectController) {\n            const selectedLevel = this.levelSelectController.getCurrentSelectedLevel();\n           \n\n            // 尝试多种方式获取全局管理器\n            let globalManager: GlobalManagerController = null;\n\n            // 方法1: 尝试查找 global_node 节点（根目录）\n            let globalManagerNode = cc.find(\"global_node\");\n            if (globalManagerNode) {\n                globalManager = globalManagerNode.getComponent(GlobalManagerController);\n          \n            } \n\n            // 方法1.1: 尝试查找 Canvas/global_node 节点\n            if (!globalManager) {\n                globalManagerNode = cc.find(\"Canvas/global_node\");\n                if (globalManagerNode) {\n                   \n                    globalManager = globalManagerNode.getComponent(GlobalManagerController);\n                    if (globalManager) {\n                        \n                    } else {\n                       \n                        // 列出节点上的所有组件\n                        const components = globalManagerNode.getComponents(cc.Component);\n                       \n                    }\n                } \n            \n            }\n\n        \n\n\n            if (globalManager) {\n                // 先切换到关卡页面\n                globalManager.setCurrentPage(PageType.LEVEL_PAGE);\n\n                // 延迟设置关卡，确保页面切换完成后再设置\n                this.scheduleOnce(() => {\n                    if (globalManager.levelPageController) {\n                        \n                        globalManager.levelPageController.setCurrentLevel(selectedLevel);\n                    }\n                }, 0.1);\n            } else {\n                cc.error(\"无法找到 GlobalManagerController 组件！请检查场景配置。\");\n                cc.error(\"请确保场景中有节点挂载了 GlobalManagerController 组件。\");\n\n                \n            }\n        }\n    }\n\n    /**\n     * 设置关卡进度（从外部调用）\n     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels\n     */\n    public setLevelProgress(levelProgressData: any) {\n        if (!this.levelSelectController) return;\n\n        // 使用 LevelSelectController 的新方法来设置关卡进度\n        this.levelSelectController.setLevelProgress(levelProgressData);\n\n        // 立即更新UI显示，确保按钮状态正确\n        this.scheduleOnce(() => {\n            this.updateUIDisplay();\n        }, 0.05); // 很短的延迟，确保关卡数据已经更新\n    }\n\n    /**\n     * 修复ScrollView的Scrollbar问题\n     */\n    private fixScrollViewScrollbar() {\n        // 如果有ScrollView引用，清除Scrollbar引用以避免错误\n        if (this.scrollView) {\n            this.scrollView.horizontalScrollBar = null;\n            this.scrollView.verticalScrollBar = null;\n        }\n\n        // 如果levelSelectController有ScrollView，也进行修复\n        if (this.levelSelectController && this.levelSelectController.scrollView) {\n            this.levelSelectController.scrollView.horizontalScrollBar = null;\n            this.levelSelectController.scrollView.verticalScrollBar = null;\n        }\n    }\n\n    /**\n     * 更新游戏按钮显示状态\n     */\n    private updateGameButtons() {\n        if (!this.levelSelectController) return;\n\n        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();\n        const levelData = this.levelSelectController.getLevelData(currentLevel);\n\n        if (!levelData) return;\n\n        // 根据关卡状态显示不同的按钮\n        const isLocked = levelData.status === LevelStatus.LOCKED;\n\n        if (this.startGameButton) {\n            this.startGameButton.node.active = !isLocked;\n            // 设置按钮文本 - 统一显示\"开始游戏\"\n            const buttonLabel = this.startGameButton.getComponentInChildren(cc.Label);\n            if (buttonLabel) {\n                buttonLabel.string = \"开始游戏\";\n            }\n        }\n\n        if (this.lockedButton) {\n            this.lockedButton.node.active = isLocked;\n        }\n    }\n\n    /**\n     * 开始游戏按钮点击事件\n     */\n    private onStartGameButtonClick() {\n        if (!this.levelSelectController) return;\n\n        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();\n        const levelData = this.levelSelectController.getLevelData(currentLevel);\n\n        if (levelData && levelData.status !== LevelStatus.LOCKED) {\n           \n            // 这里添加进入游戏的逻辑\n            this.enterSelectedLevel();\n        }\n    }\n\n    /**\n     * 未解锁按钮点击事件\n     */\n    private onLockedButtonClick() {\n        const currentLevel = this.levelSelectController.getCurrentSelectedLevel();\n      \n        // 这里可以添加提示用户关卡未解锁的逻辑\n        // 例如显示提示弹窗等\n    }\n\n    /**\n     * 关卡选择变化回调\n     */\n    private onLevelSelectionChanged(levelNumber: number) {\n\n        this.updateUIDisplay();\n    }\n}\n"]}