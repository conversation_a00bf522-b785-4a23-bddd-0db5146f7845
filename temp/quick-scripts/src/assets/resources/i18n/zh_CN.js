"use strict";
cc._RF.push(module, '83ee5xvZ1VBLb3UezEVIqou', 'zh_CN');
// resources/i18n/zh_CN.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',
    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',
    upSeat: '加入游戏',
    downSeat: '退出游戏',
    startGame: '开始',
    readyGame: '准备',
    cancelGame: '取消准备',
    cancel: '取消',
    confirm: '确定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音乐',
    sound: '音效',
    join: '加入',
    create: '创建',
    auto: '匹配',
    Room: '房间',
    room_number: '房间号',
    copy: '复制',
    game_amount: '游戏费用',
    player_numbers: '玩家数量:',
    room_exist: '房间不存在',
    enter_room_number: '输入房间号',
    free: '免费',
    players: '玩家',
    Player: '玩家',
    Tickets: '门票',
    Empty: '空位',
    nextlevel: '下一关',
    relevel: '再玩一次',
    danjiguize: "单机规则",
    lianjuguize: "联机规则",
    dtips1: "游戏简介：",
    dtips2: "安全区：",
    dtips3: "雷区：",
    dtips4: "游戏目标：",
    dtips5: "标记：",
    dtips6: "提示：",
    dinfo1: "游戏中存在一些格子，翻开后分为数字格子，空白格子和地雷，玩家可以通过点击翻开新格子，数字格子可以同帮助玩家获得周围相邻格子中存在的地雷数量，若翻到空白格子会继续扩散翻牌，直到翻到数字格子才停止，利用游戏规则，顺利通关吧。",
    dinfo2: "数字格子与空白格子统称为安全区",
    dinfo3: "翻到雷区，会导致游戏失败。",
    dinfo4: "在不触发任何雷区的情况下，揭示游戏区域中的所有安全格子。",
    dinfo5: "玩家可以通过对格子长按，插旗子来标记雷区，标记不会翻开该格子。",
    dinfo6: "玩家每局可获得一次提示，仅本局生效，点击提示可以帮助玩家显示出一个安全的格子。一局最多使用4次提示。",
    ltips1: "游戏简介：",
    ltips2: "游戏人数：",
    ltips3: "回合时间：",
    ltips4: "游戏目标：",
    ltips5: "标记：",
    ltips6: "得分规则：",
    linfo1: "与单机模式相同，格子的样式一致。每个回合所有玩家同时选择任意一个格子，时间结束后或所有人全部选择完毕后，展示所有人的选择情况并根据格子的内容进行加减分，待所有格子都被选完，游戏结束。争取得到更多的分数吧！",
    linfo2: "2人/3人/4人",
    linfo3: "20秒",
    linfo4: "利用游戏规则，进行得分，分数最高者获得胜利。",
    linfo5: "长按可对格子进行雷区标记，若该格子为雷区，则可额外获得加分。",
    linfo6: "1. 每回合最先选择格子的玩家获得1分。\n2. 单击翻开格子，若为安全格子则加6分，为雷区则扣12分。\n3. 长按进行标记翻开格子，若为雷区则加10分，为安全区则不加分。\n4. 多人同时选择一个格子，根据选择的对错结果，进行平均加分，例如两人单击选择同一个格子，该格子为安全区，则每人得3分。",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = exports.language;

cc._RF.pop();