export const language = {
    //這部分是通用的
    kickout1: '您被請出房間',
    LeaveRoom: '房間已解散',
    InsufficientBalance: '餘額不足，去儲值',
    GameRouteNotFound: '遊戲路線異常',
    NetworkError: '網絡異常',
    RoomIsFull: '房間已滿',
    EnterRoomNumber: '輸入房間號',
    GetUserInfoFailed: '獲取用戶資訊失敗',
    RoomDoesNotExist: '房間不存在',
    FailedToDeductGoldCoins: '扣除金幣失敗',
    ExitApplication: '確定退出遊戲？',
    QuitTheGame: '退出後將無法返回遊戲。',

    NotEnoughPlayers: '玩家數量不足',
    TheGameIsFullOfPlayers: '玩家數量已滿',
    kickout2: '是否將 {0} 請出房間?',

    upSeat: '加入遊戲',
    downSeat: '退出遊戲',
    startGame: '開始',
    readyGame: '準備',
    cancelGame: '取消準備',
    cancel: '取消',
    confirm: '確定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音樂',
    sound: '音效',
    join: '進入', //加入
    create: '創建', //创建
    auto: '匹配',
    Room: '房間',
    room_number: '房號', //房间号
    copy: '複製', //复制
    game_amount: '遊戲費用', //游戏费用
    player_numbers: '玩家數量:', //玩家数量
    room_exist: '房間不存在',//房间不存在
    enter_room_number: '輸入房間號',//输入房间号
    free: '免費',
    players: '玩家', //玩家
    Player: '玩家',
    Tickets: '門票',
    Empty: '空位',

    nextlevel: '下一關', //下一關  
    relevel: '再玩一次', //再來一局 

    danjiguize: "單機規則",
    lianjuguize: "聯機規則",

    dtips1: "遊戲簡介：",
    dtips2: "安全區：",
    dtips3: "雷區：",
    dtips4: "遊戲目標：",
    dtips5: "標記：",
    dtips6: "提示：",

    dinfo1: "遊戲中存在一些格子，翻開後分為數字格子、空白格子和地雷。玩家可以通過點擊翻開新格子，數字格子可以幫助玩家獲得周圍相鄰格子中存在的地雷數量。若翻到空白格子會繼續擴散翻牌，直到翻到數字格子才停止。利用遊戲規則，順利通關吧。",
    dinfo2: "數字格子與空白格子統稱為安全區。",
    dinfo3: "翻到雷區，會導致遊戲失敗。",
    dinfo4: "在不觸發任何雷區的情況下，揭示遊戲區域中的所有安全格子。",
    dinfo5: "玩家可以通過對格子長按，插旗子來標記雷區，標記不會翻開該格子。",
    dinfo6: "玩家每局可獲得一次提示，僅本局生效。點擊提示可以幫助玩家顯示出一個安全的格子。一局最多使用4次提示。",


    ltips1: "遊戲簡介：",
    ltips2: "遊戲人數：",
    ltips3: "回合時間：",
    ltips4: "遊戲目標：",
    ltips5: "標記：",
    ltips6: "得分規則：",

    linfo1: "與單機模式相同，格子的樣式一致。每個回合所有玩家同時選擇任意一個格子，時間結束後或所有人全部選擇完畢後，展示所有人的選擇情況並根據格子的內容進行加減分。待所有格子都被選完，遊戲結束。爭取得到更多的分數吧！",
    linfo2: "2人/3人/4人",
    linfo3: "20秒",
    linfo4: "利用遊戲規則進行得分，分數最高者獲得勝利。",
    linfo5: "長按可對格子進行雷區標記，若該格子為雷區，則可額外獲得加分。",
    linfo6: "1. 每回合最先選擇格子的玩家獲得1分。\n2. 單擊翻開格子，若為安全格子則加6分，為雷區則扣12分。\n3. 長按進行標記翻開格子，若為雷區則加10分，為安全區則不加分。\n4. 多人同時選擇一個格子，根據選擇的對錯結果進行平均加分。例如兩人單擊選擇同一個格子，該格子為安全區，則每人得3分。"


};

const cocos = cc as any;
if (!cocos.Jou_i18n) cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_HK = language;