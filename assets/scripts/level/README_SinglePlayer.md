# 单机模式控制器使用说明

## 概述

`SinglePlayerController` 是一个专门为扫雷游戏单机模式设计的控制器，支持多种地图大小配置，并通过 `LevelClickBlock` 消息与后端通信。

## 主要特性

1. **多地图支持**: 支持 8x8、9x9、9x10、10x10 四种地图大小
2. **独立消息系统**: 使用 `LevelClickBlock` 消息，与联机模式完全分离
3. **即时响应**: 操作立即生效，无延迟展示
4. **自由标记**: 支持标记状态的自由切换
5. **无时间限制**: 没有回合制和倒计时限制

## 地图配置

### 支持的地图类型

| 地图类型 | 棋盘大小 | 格子大小 | 行列数 |
|---------|---------|---------|-------|
| 8x8     | 752x845 | 88x88   | 8x8   |
| 9x9     | 752x747 | 76x76   | 9x9   |
| 9x10    | 752x830 | 78x78   | 9x10  |
| 10x10   | 752x745 | 69x69   | 10x10 |

## 使用方法

### 1. 在编辑器中配置

在 `LevelPageController` 中添加 `SinglePlayerController` 组件的引用：

```typescript
@property(SinglePlayerController)
singlePlayerController: SinglePlayerController = null;
```

### 2. 设置预制体

在 `SinglePlayerController` 中配置以下预制体：

- `boomPrefab`: 炸弹预制体
- `biaojiPrefab`: 标记预制体
- `boom1Prefab` ~ `boom8Prefab`: 数字1-8预制体

### 3. 初始化控制器

```typescript
// 设置棋盘节点
singlePlayerController.setBoardNode(boardNode);

// 设置地图类型
singlePlayerController.setMapType("8x8");

// 清空之前的预制体
singlePlayerController.clearAllPrefabs();
```

## 操作说明

### 点击操作
- **短按**: 挖掘方块 (action = 1)
- **长按**: 标记/取消标记 (action = 2)

### 消息流程

1. **发送消息**: 
   - 消息类型: `LevelClickBlock`
   - 参数: `{x, y, action}`

2. **接收响应**:
   - 消息类型: `LevelClickBlock`
   - 响应: `{x, y, action, result}`

### 结果处理

- `result = "mine"`: 显示炸弹预制体
- `result = "marked"`: 显示标记预制体
- `result = "unmarked"`: 移除标记预制体
- `result = 数字`: 显示对应数字预制体

## 与联机模式的区别

| 特性 | 联机模式 | 单机模式 |
|-----|---------|---------|
| 消息类型 | `ClickBlock` | `LevelClickBlock` |
| 头像生成 | 需要 | 不需要 |
| 时间限制 | 有回合制 | 无限制 |
| 标记切换 | 受限 | 自由切换 |
| 响应速度 | 有延迟 | 即时响应 |

## API 参考

### 公共方法

- `setMapType(mapType: string)`: 设置地图类型
- `setBoardNode(boardNode: Node)`: 设置棋盘节点
- `clearAllPrefabs()`: 清空所有预制体

### 私有方法

- `initBoard()`: 初始化棋盘
- `onGridClick(x, y)`: 处理格子点击
- `onGridLongPress(x, y)`: 处理格子长按
- `createBoomPrefab(x, y)`: 创建炸弹预制体
- `createBiaojiPrefab(x, y)`: 创建标记预制体
- `createNumberPrefab(x, y, number)`: 创建数字预制体

## 注意事项

1. 确保在编辑器中正确配置所有预制体引用
2. 棋盘节点必须包含足够的子节点作为格子
3. 后端需要实现 `LevelClickBlock` 消息处理
4. 特殊关卡（S001-S006）暂时不使用单机控制器
