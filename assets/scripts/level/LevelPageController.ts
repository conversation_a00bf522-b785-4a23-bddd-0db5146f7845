// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { ExtendLevelInfoRequest, ExtendLevelInfoResponse } from "../bean/GameBean";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import LeaveDialogController from "../hall/LeaveDialogController";
import { Tools } from "../util/Tools";
import { Config } from "../util/Config";
import SinglePlayerController from "./SinglePlayerController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LevelPageController extends cc.Component {

    // 返回按钮
    @property(cc.Node)
    backButton: cc.Node = null;

    // 开始游戏按钮
    @property(cc.Button)
    startGameButton: cc.Button = null;

    // 地雷数UI标签
    @property(cc.Label)
    mineCountLabel: cc.Label = null;

    // 当前关卡数UI标签
    @property(cc.Label)
    currentLevelLabel: cc.Label = null;

    // 退出游戏弹窗
    @property(LeaveDialogController)
    leaveDialogController: LeaveDialogController = null;

    // level_page节点
    @property(cc.Node)
    levelPageNode: cc.Node = null;

    // game_map_1节点
    @property(cc.Node)
    gameMap1Node: cc.Node = null;

    // game_map_2节点
    @property(cc.Node)
    gameMap2Node: cc.Node = null;

    // 方形地图节点引用
    @property(cc.Node)
    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8

    @property(cc.Node)
    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9

    @property(cc.Node)
    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9

    @property(cc.Node)
    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10

    @property(cc.Node)
    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10

    // 特殊关卡节点引用
    @property(cc.Node)
    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001

    @property(cc.Node)
    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002

    @property(cc.Node)
    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003

    @property(cc.Node)
    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004

    @property(cc.Node)
    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005

    @property(cc.Node)
    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006

    // 单机模式控制器
    @property(SinglePlayerController)
    singlePlayerController: SinglePlayerController = null;

    // 当前关卡数据
    private currentLevel: number = 1;
    private currentLevelInfo: ExtendLevelInfoResponse = null;
    private currentRoomId: number = 0; // 当前关卡游戏的房间ID

    onLoad() {
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools.imageButtonClick(this.backButton, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {
                this.onBackButtonClick();
            });
        }

        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }

        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    }

    start() {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();

    }

    /**
     * 返回按钮点击事件
     */
    private onBackButtonClick() {
      

        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
           
            this.leaveDialogController.show(1, () => {
               
            }, this.currentRoomId);
        } else {
            cc.warn("LeaveDialogController 未配置");
        }
    }

    /**
     * 开始游戏按钮点击事件
     */
    private onStartGameButtonClick() {
       
        // 发送ExtendLevelInfo消息到后端获取地图数据
        const request: ExtendLevelInfoRequest = {
            levelId: this.currentLevel
        };

      
        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, request);
    }

    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {
    

        this.currentLevelInfo = levelInfo;

        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
           
        }

        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);

        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
       
        this.enterLevel(this.currentLevel);
    }

    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    private updateMineCountUI(mineCount: number) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
           
        }
    }

    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    private updateCurrentLevelUI(levelNumber: number) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = `第${levelNumber}关`;
           
        }
    }

    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    private enterLevel(levelNumber: number) {
    

        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);

     

        // 先隐藏所有地图容器
        this.hideAllMapContainers();

        // 根据关卡数显示对应的地图节点并设置单机模式控制器
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
            this.setupSinglePlayerController("8x8", this.qipan8x8Node);
        } else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
            // 特殊关卡暂时不使用单机控制器
        } else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
            this.setupSinglePlayerController("8x8", this.qipan8x9Node); // 注意：8x9实际使用8x8配置
        } else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
            // 特殊关卡暂时不使用单机控制器
        } else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
            this.setupSinglePlayerController("9x9", this.qipan9x9Node);
        } else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
            // 特殊关卡暂时不使用单机控制器
        } else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
            this.setupSinglePlayerController("9x10", this.qipan9x10Node);
        } else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
            // 特殊关卡暂时不使用单机控制器
        } else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
            this.setupSinglePlayerController("10x10", this.qipan10x10Node);
        } else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
            // 特殊关卡暂时不使用单机控制器
        } else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
            this.setupSinglePlayerController("10x10", this.qipan10x10Node);
        } else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
            // 特殊关卡暂时不使用单机控制器
        } else {
            cc.warn(`未知的关卡编号: ${levelNumber}`);
        }
    }

    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    private showMapNode(mapNode: cc.Node, mapName: string) {
        if (mapNode) {
           
            mapNode.active = true;

            // 检查父节点链是否都是激活状态
            let currentNode = mapNode.parent;
            let parentChain = [];
            while (currentNode) {
                // 避免访问场景节点的 active 属性
                if (currentNode.name === 'game_scene' || currentNode.name === 'Scene') {
                    parentChain.push(`${currentNode.name}(Scene)`);
                } else {
                    parentChain.push(`${currentNode.name}(${currentNode.active})`);
                }
                currentNode = currentNode.parent;
            }
          

           
        } else {
            cc.warn(`❌ 地图节点未找到: ${mapName}`);
            cc.warn(`请在编辑器中为 LevelPageController 配置 ${mapName} 节点属性`);
        }
    }

    /**
     * 隐藏所有地图节点
     */
    private hideAllMapNodes() {
        const allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];

        allMapNodes.forEach(node => {
            if (node) {
                node.active = false;
            }
        });
    }

    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    public setCurrentLevel(levelNumber: number) {
      
        this.currentLevel = levelNumber;


        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    }

    /**
     * 获取当前关卡编号
     */
    public getCurrentLevel(): number {
        return this.currentLevel;
    }

    /**
     * 获取当前关卡信息
     */
    public getCurrentLevelInfo(): ExtendLevelInfoResponse {
        return this.currentLevelInfo;
    }

    /**
     * 获取当前房间ID
     */
    public getCurrentRoomId(): number {
        return this.currentRoomId;
    }

    /**
     * 隐藏所有地图容器
     */
    private hideAllMapContainers() {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
          
        }

        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
            
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
           
        }

        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    }

    /**
     * 显示 game_map_1 容器（方形地图）
     */
    private showGameMap1() {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
            
        } else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    }

    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    private showGameMap2() {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;

        } else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    }

    /**
     * 设置单机模式控制器
     * @param mapType 地图类型
     * @param boardNode 棋盘节点
     */
    private setupSinglePlayerController(mapType: string, boardNode: cc.Node) {
        if (this.singlePlayerController && boardNode) {
            // 设置棋盘节点
            this.singlePlayerController.setBoardNode(boardNode);

            // 设置地图类型
            this.singlePlayerController.setMapType(mapType);

            // 清空之前的预制体
            this.singlePlayerController.clearAllPrefabs();

            console.log(`单机模式控制器已设置: ${mapType}, 棋盘节点: ${boardNode.name}`);
        } else {
            if (!this.singlePlayerController) {
                cc.warn("❌ SinglePlayerController 未配置");
            }
            if (!boardNode) {
                cc.warn("❌ 棋盘节点未找到");
            }
        }
    }
}
