import SinglePlayerController from './SinglePlayerController';

const { ccclass, property } = cc._decorator;

/**
 * 单机模式控制器测试脚本
 * 用于测试和演示SinglePlayerController的功能
 */
@ccclass
export default class SinglePlayerTest extends cc.Component {

    @property(SinglePlayerController)
    singlePlayerController: SinglePlayerController = null;

    @property(cc.Node)
    testBoardNode: cc.Node = null;

    @property(cc.Button)
    test8x8Button: cc.Button = null;

    @property(cc.Button)
    test9x9Button: cc.Button = null;

    @property(cc.Button)
    test9x10Button: cc.Button = null;

    @property(cc.Button)
    test10x10Button: cc.Button = null;

    @property(cc.Button)
    clearButton: cc.Button = null;

    @property(cc.Label)
    statusLabel: cc.Label = null;

    onLoad() {
        this.setupButtons();
        this.updateStatus("单机模式控制器测试就绪");
    }

    private setupButtons() {
        if (this.test8x8Button) {
            this.test8x8Button.node.on('click', () => this.testMapType("8x8"), this);
        }

        if (this.test9x9Button) {
            this.test9x9Button.node.on('click', () => this.testMapType("9x9"), this);
        }

        if (this.test9x10Button) {
            this.test9x10Button.node.on('click', () => this.testMapType("9x10"), this);
        }

        if (this.test10x10Button) {
            this.test10x10Button.node.on('click', () => this.testMapType("10x10"), this);
        }

        if (this.clearButton) {
            this.clearButton.node.on('click', this.clearAllPrefabs, this);
        }
    }

    private testMapType(mapType: string) {
        if (!this.singlePlayerController) {
            this.updateStatus("错误: SinglePlayerController 未配置");
            return;
        }

        if (!this.testBoardNode) {
            this.updateStatus("错误: 测试棋盘节点未配置");
            return;
        }

        try {
            // 设置棋盘节点
            this.singlePlayerController.setBoardNode(this.testBoardNode);
            
            // 设置地图类型
            this.singlePlayerController.setMapType(mapType);
            
            // 清空之前的预制体
            this.singlePlayerController.clearAllPrefabs();
            
            this.updateStatus(`已切换到 ${mapType} 地图模式`);
            
            console.log(`测试: 切换到 ${mapType} 地图模式`);
            
        } catch (error) {
            this.updateStatus(`错误: ${error.message}`);
            console.error("测试失败:", error);
        }
    }

    private clearAllPrefabs() {
        if (!this.singlePlayerController) {
            this.updateStatus("错误: SinglePlayerController 未配置");
            return;
        }

        try {
            this.singlePlayerController.clearAllPrefabs();
            this.updateStatus("已清空所有预制体");
            console.log("测试: 清空所有预制体");
        } catch (error) {
            this.updateStatus(`错误: ${error.message}`);
            console.error("清空失败:", error);
        }
    }

    private updateStatus(message: string) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log(`[SinglePlayerTest] ${message}`);
    }

    /**
     * 模拟后端响应测试
     */
    public simulateBackendResponse(x: number, y: number, action: number, result: string | number) {
        if (!this.singlePlayerController) {
            console.error("SinglePlayerController 未配置");
            return;
        }

        // 模拟后端响应数据
        const mockResponse = {
            msgId: "LevelClickBlock",
            data: {
                x: x,
                y: y,
                action: action,
                result: result
            }
        };

        console.log("模拟后端响应:", mockResponse);
        
        // 直接调用消息处理方法（仅用于测试）
        // 注意：这里需要访问私有方法，实际使用中应该通过WebSocket接收消息
        // this.singlePlayerController['handleLevelClickBlockResponse'](mockResponse.data);
    }

    /**
     * 测试各种操作结果
     */
    public runTestSuite() {
        console.log("开始运行测试套件...");
        
        // 测试挖掘操作 - 数字结果
        this.scheduleOnce(() => {
            this.simulateBackendResponse(0, 0, 1, 1);
            console.log("测试: 挖掘操作 - 显示数字1");
        }, 1);

        // 测试挖掘操作 - 炸弹结果
        this.scheduleOnce(() => {
            this.simulateBackendResponse(1, 0, 1, "mine");
            console.log("测试: 挖掘操作 - 显示炸弹");
        }, 2);

        // 测试标记操作
        this.scheduleOnce(() => {
            this.simulateBackendResponse(2, 0, 2, "marked");
            console.log("测试: 标记操作 - 显示标记");
        }, 3);

        // 测试取消标记操作
        this.scheduleOnce(() => {
            this.simulateBackendResponse(2, 0, 2, "unmarked");
            console.log("测试: 取消标记操作 - 移除标记");
        }, 4);

        this.updateStatus("测试套件运行中...");
    }
}
