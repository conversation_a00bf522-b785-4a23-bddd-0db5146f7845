import { WebSocketManager } from '../net/WebSocketManager';
import { MessageId } from '../net/MessageId';
import { ClickBlockRequest, LevelClickBlockResponse } from '../bean/GameBean';
import { EventType } from '../common/EventCenter';
import { GameMgr } from '../common/GameMgr';

const { ccclass, property } = cc._decorator;

// 地图配置接口
interface MapConfig {
    boardSize: { width: number, height: number };  // 棋盘大小
    gridSize: { width: number, height: number };   // 格子大小
    boardDimensions: { rows: number, cols: number }; // 行列数
}

// 格子数据接口
interface GridData {
    x: number;
    y: number;
    hasPlayer: boolean;
    isRevealed: boolean;
    isMarked: boolean;
}

@ccclass
export default class SinglePlayerController extends cc.Component {

    @property(cc.Node)
    boardNode: cc.Node = null; // 棋盘节点

    @property(cc.Prefab)
    boomPrefab: cc.Prefab = null; // 炸弹预制体

    @property(cc.Prefab)
    biaojiPrefab: cc.Prefab = null; // 标记预制体

    @property(cc.Prefab)
    boom1Prefab: cc.Prefab = null; // 数字1预制体
    @property(cc.Prefab)
    boom2Prefab: cc.Prefab = null; // 数字2预制体
    @property(cc.Prefab)
    boom3Prefab: cc.Prefab = null; // 数字3预制体
    @property(cc.Prefab)
    boom4Prefab: cc.Prefab = null; // 数字4预制体
    @property(cc.Prefab)
    boom5Prefab: cc.Prefab = null; // 数字5预制体
    @property(cc.Prefab)
    boom6Prefab: cc.Prefab = null; // 数字6预制体
    @property(cc.Prefab)
    boom7Prefab: cc.Prefab = null; // 数字7预制体
    @property(cc.Prefab)
    boom8Prefab: cc.Prefab = null; // 数字8预制体

    // 地图配置
    private mapConfigs: { [key: string]: MapConfig } = {
        "8x8": {
            boardSize: { width: 752, height: 845 },
            gridSize: { width: 88, height: 88 },
            boardDimensions: { rows: 8, cols: 8 }
        },
        "9x9": {
            boardSize: { width: 752, height: 747 },
            gridSize: { width: 76, height: 76 },
            boardDimensions: { rows: 9, cols: 9 }
        },
        "9x10": {
            boardSize: { width: 752, height: 830 },
            gridSize: { width: 78, height: 78 },
            boardDimensions: { rows: 9, cols: 10 }
        },
        "10x10": {
            boardSize: { width: 752, height: 745 },
            gridSize: { width: 69, height: 69 },
            boardDimensions: { rows: 10, cols: 10 }
        }
    };

    // 当前地图配置
    private currentMapConfig: MapConfig = null;
    private currentMapType: string = "8x8"; // 默认8x8

    // 格子数据存储
    private gridData: GridData[][] = [];
    private gridNodes: cc.Node[][] = [];

    onLoad() {
        // 注册消息监听
        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);

        // 设置默认地图配置
        this.setMapType(this.currentMapType);
        this.initBoard();
    }

    onDestroy() {
        // 取消消息监听
        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);
    }

    /**
     * 设置地图类型
     * @param mapType 地图类型 "8x8", "9x9", "9x10", "10x10"
     */
    public setMapType(mapType: string) {
        if (this.mapConfigs[mapType]) {
            this.currentMapType = mapType;
            this.currentMapConfig = this.mapConfigs[mapType];
            console.log(`设置地图类型为: ${mapType}`, this.currentMapConfig);

            // 重新初始化棋盘
            if (this.boardNode) {
                this.initBoard();
            }
        } else {
            console.error(`不支持的地图类型: ${mapType}`);
        }
    }

    /**
     * 设置棋盘节点
     * @param boardNode 棋盘节点
     */
    public setBoardNode(boardNode: cc.Node) {
        this.boardNode = boardNode;
        if (this.currentMapConfig) {
            this.initBoard();
        }
    }

    /**
     * 初始化棋盘
     */
    private initBoard() {
        if (!this.currentMapConfig || !this.boardNode) {
            console.error("地图配置或棋盘节点未设置");
            return;
        }

        const { rows, cols } = this.currentMapConfig.boardDimensions;
        
        // 初始化格子数据
        this.gridData = [];
        this.gridNodes = [];
        
        for (let x = 0; x < cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (let y = 0; y < rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    hasPlayer: false,
                    isRevealed: false,
                    isMarked: false
                };
            }
        }

        this.enableTouchForExistingGrids();
    }

    /**
     * 为现有格子启用触摸事件
     */
    private enableTouchForExistingGrids() {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }

        const { rows, cols } = this.currentMapConfig.boardDimensions;

        // 遍历棋盘的所有子节点，为格子添加触摸事件
        this.boardNode.children.forEach((child: cc.Node, index: number) => {
            // 根据子节点的索引计算格子坐标
            const x = index % cols;
            const y = Math.floor(index / cols);

            if (x < cols && y < rows) {
                this.addTouchEventToGrid(child, x, y);
                this.gridNodes[x][y] = child;
            }
        });
    }

    /**
     * 为格子添加触摸事件
     */
    private addTouchEventToGrid(gridNode: cc.Node, x: number, y: number) {
        // 长按相关变量
        let isLongPressing = false;
        let longPressTimer = 0;
        let longPressCallback: Function = null;
        const LONG_PRESS_TIME = 1.0; // 1秒长按时间

        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {
            isLongPressing = true;
            longPressTimer = 0;

            // 开始长按检测
            longPressCallback = () => {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            this.schedule(longPressCallback, 0.1);
        }, this);

        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, (_event: cc.Event.EventTouch) => {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                this.onGridClick(x, y);
            }

            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);

        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {
            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);
    }

    /**
     * 格子点击事件 - 发送挖掘操作
     */
    private onGridClick(x: number, y: number) {
        console.log(`格子点击: (${x}, ${y})`);

        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }

        // 检查该位置是否已经被揭开或标记
        const gridData = this.gridData[x][y];
        if (gridData.isRevealed || gridData.hasPlayer) {
            return;
        }

        // 发送挖掘操作到后端
        this.sendLevelClickBlock(x, y, 1);
    }

    /**
     * 格子长按事件 - 发送标记操作
     */
    private onGridLongPress(x: number, y: number) {
        console.log(`格子长按: (${x}, ${y})`);

        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }

        // 检查该位置是否已经被揭开
        const gridData = this.gridData[x][y];
        if (gridData.isRevealed) {
            return;
        }

        // 发送标记操作到后端
        this.sendLevelClickBlock(x, y, 2);
    }

    /**
     * 发送关卡点击方块消息
     */
    private sendLevelClickBlock(x: number, y: number, action: number) {
        const clickData: ClickBlockRequest = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };

        console.log(`发送LevelClickBlock消息:`, clickData);
        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLevelClickBlock, clickData);
    }

    /**
     * 处理后端消息
     */
    private onReceiveMessage(data: any) {
        if (data.msgId === MessageId.MsgTypeLevelClickBlock) {
            this.handleLevelClickBlockResponse(data.data);
        }
    }

    /**
     * 处理关卡点击方块响应
     */
    private handleLevelClickBlockResponse(responseData: LevelClickBlockResponse) {
        console.log("收到LevelClickBlock响应:", responseData);

        const { x, y, result, action } = responseData;
        
        // 更新格子状态
        if (this.isValidCoordinate(x, y)) {
            const gridData = this.gridData[x][y];
            
            if (action === 1) {
                // 挖掘操作
                gridData.isRevealed = true;
                gridData.hasPlayer = true;
                
                // 根据结果显示相应内容
                if (result === "mine") {
                    // 显示炸弹
                    this.createBoomPrefab(x, y);
                } else if (typeof result === "number") {
                    // 显示数字
                    this.createNumberPrefab(x, y, result);
                }
            } else if (action === 2) {
                // 标记操作
                if (result === "marked") {
                    // 添加标记
                    gridData.isMarked = true;
                    gridData.hasPlayer = true;
                    this.createBiaojiPrefab(x, y);
                } else if (result === "unmarked") {
                    // 取消标记
                    gridData.isMarked = false;
                    gridData.hasPlayer = false;
                    this.removePrefabAt(x, y);
                }
            }
        }
    }

    /**
     * 检查坐标是否有效
     */
    private isValidCoordinate(x: number, y: number): boolean {
        const { rows, cols } = this.currentMapConfig.boardDimensions;
        return x >= 0 && x < cols && y >= 0 && y < rows;
    }

    /**
     * 计算预制体的精确位置
     */
    private calculatePrefabPosition(x: number, y: number): cc.Vec2 {
        const { boardSize, gridSize } = this.currentMapConfig;

        // 计算起始位置（左下角）
        const startX = -(boardSize.width / 2) + (gridSize.width / 2);
        const startY = -(boardSize.height / 2) + (gridSize.height / 2);

        // 计算最终位置
        const finalX = startX + (x * gridSize.width);
        const finalY = startY + (y * gridSize.height);

        return cc.v2(finalX, finalY);
    }

    /**
     * 在指定位置创建炸弹预制体
     */
    private createBoomPrefab(x: number, y: number) {
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置");
            return;
        }

        const boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";

        const position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);

        this.boardNode.addChild(boomNode);

        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
    }

    /**
     * 在指定位置创建标记预制体
     */
    private createBiaojiPrefab(x: number, y: number) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置");
            return;
        }

        const biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";

        const position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);

        this.boardNode.addChild(biaojiNode);

        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    }

    /**
     * 在指定位置创建数字预制体
     */
    private createNumberPrefab(x: number, y: number, number: number) {
        if (number === 0) {
            return; // 0不需要显示
        }

        let prefab: cc.Prefab = null;
        switch (number) {
            case 1: prefab = this.boom1Prefab; break;
            case 2: prefab = this.boom2Prefab; break;
            case 3: prefab = this.boom3Prefab; break;
            case 4: prefab = this.boom4Prefab; break;
            case 5: prefab = this.boom5Prefab; break;
            case 6: prefab = this.boom6Prefab; break;
            case 7: prefab = this.boom7Prefab; break;
            case 8: prefab = this.boom8Prefab; break;
            default:
                console.error(`不支持的数字: ${number}`);
                return;
        }

        if (!prefab) {
            console.error(`boom${number}Prefab 预制体未设置`);
            return;
        }

        const numberNode = cc.instantiate(prefab);
        numberNode.name = `Boom${number}`;

        const position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);

        this.boardNode.addChild(numberNode);

        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    }

    /**
     * 移除指定位置的预制体
     */
    private removePrefabAt(x: number, y: number) {
        // 查找并移除该位置的预制体
        const position = this.calculatePrefabPosition(x, y);
        const tolerance = 10; // 位置容差

        this.boardNode.children.forEach((child: cc.Node) => {
            const childPos = child.getPosition();
            if (Math.abs(childPos.x - position.x) < tolerance &&
                Math.abs(childPos.y - position.y) < tolerance) {
                if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                    child.removeFromParent();
                }
            }
        });
    }

    /**
     * 清空所有预制体
     */
    public clearAllPrefabs() {
        this.boardNode.children.forEach((child: cc.Node) => {
            if (child.name.includes("Biaoji") || child.name.includes("Boom")) {
                child.removeFromParent();
            }
        });

        // 重置格子数据
        const { rows, cols } = this.currentMapConfig.boardDimensions;
        for (let x = 0; x < cols; x++) {
            for (let y = 0; y < rows; y++) {
                this.gridData[x][y].hasPlayer = false;
                this.gridData[x][y].isRevealed = false;
                this.gridData[x][y].isMarked = false;
            }
        }
    }
}
